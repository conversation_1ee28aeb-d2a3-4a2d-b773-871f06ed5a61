"""
租屋套餐取消相关API路由模块
提供租屋套餐取消相关功能，包含以下特性：
1. 获取可取消套餐列表
2. 执行套餐取消操作
3. 自动重试机制
4. 错误处理和日志记录
"""

import aiohttp
from fastapi import APIRouter
from pydantic import BaseModel
from logger import log_async_task
import asyncio
from get_user_id import get_user_id_from_params

router = APIRouter()


# 请求模型定义
class PackageListRequest(BaseModel):
    """获取套餐列表的请求模型"""
    params: str  # 用户认证参数


class DoBackRequest(BaseModel):
    """执行套餐取消的请求模型"""
    package_str: str  # 套餐标识
    params: str  # 用户认证参数


# HTTP 客户端管理
class HTTPClient:
    """
    HTTP客户端管理类
    提供全局共享的aiohttp会话实例，优化连接复用
    """
    _session = None

    @classmethod
    async def get_session(cls):
        """
        获取或创建aiohttp会话实例
        
        Returns:
            aiohttp.ClientSession: 会话实例
        """
        if cls._session is None or cls._session.closed:
            cls._session = aiohttp.ClientSession(
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                },
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return cls._session


# 统一的 API 请求处理函数
async def make_api_request_with_retry(url: str, data: dict, user_id: str, action_page: str, max_retries: int = 2,
                                      retry_delay: int = 3):
    """
    统一的 API 请求处理函数，包含重试机制
    
    处理逻辑：
    1. 发送API请求
    2. 处理重复操作情况
    3. 处理错误情况
    4. 记录操作日志
    
    Args:
        url: 请求URL
        data: 请求数据
        user_id: 用户ID
        action_page: 操作页面
        max_retries: 最大重试次数
        retry_delay: 重试间隔(秒)
    
    Returns:
        Dict: 标准响应对象
    """
    retries = 0

    while retries <= max_retries:
        try:
            session = await HTTPClient.get_session()
            async with session.post(url, data=data) as response:
                result = await response.json()
                print(result)
                # 记录日志
                log_desc = str(result)

                # 处理重复操作的情况
                if (result.get("code") == "000001" and
                        "請勿重複操作" in result.get("errorMsg", "")):

                    # 记录重试日志
                    asyncio.create_task(log_async_task(
                        user_id=user_id,
                        action_page=action_page,
                        action_result="重试",
                        action_desc=f"第{retries + 1}次重试：{log_desc}"
                    ))

                    if retries < max_retries:
                        retries += 1
                        await asyncio.sleep(retry_delay)
                        continue

                # 记录最终结果
                asyncio.create_task(log_async_task(
                    user_id=user_id,
                    action_page=action_page,
                    action_result="成功" if result.get("code") == "000000" else "失败",
                    action_desc=f"最终结果：{log_desc}"
                ))

                # 统一返回格式
                if result.get("code") == "000000":
                    return {
                        "code": "000000",
                        "message": "成功",
                        "data": {
                            "list": result.get("list", [])
                        }
                    }
                else:
                    # 处理空错误信息的情况
                    error_msg = result.get("errorMsg")
                    if result.get("code") == "000001" and not error_msg:
                        error_msg = "經查詢，您的賬號內無符合取消條件的出租套餐\n\n取消條件：\n①購買時間小於24小時\n②套餐未曾使用"

                    return {
                        "code": "000001",
                        "message": error_msg or "操作失敗",
                        "data": None
                    }

        except Exception as e:
            # 记录异常日志
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page=action_page,
                action_result="异常",
                action_desc=f"第{retries + 1}次请求异常：{str(e)}"
            ))

            if retries < max_retries:
                retries += 1
                await asyncio.sleep(retry_delay)
                continue

            # 所有重试都失败后记录最终错误
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page=action_page,
                action_result="失敗",
                action_desc=f"重试{max_retries}次后失败：{str(e)}"
            ))

            return {
                "code": "000001",
                "message": "請求失敗，請稍後重試",
                "data": None
            }


@router.post("/cancel/rent/packagelist")
async def handle_rent_ware_list(request: PackageListRequest):
    """
    获取套餐列表
    
    处理逻辑：
    1. 从请求参数中获取用户ID
    2. 调用API获取套餐列表
    3. 处理响应数据
    
    Args:
        request: 包含用户认证参数的请求对象
    
    Returns:
        Dict: 包含套餐列表的响应对象
    """
    user_id = get_user_id_from_params(request.params)

    data = {
        'params': f'{request.params}',
        'back_type': 3
    }

    return await make_api_request_with_retry(
        url="https://api.591.com.tw/api/sobot/index?strategy=BackMoney&step_name=showWareList",
        data=data,
        user_id=user_id,
        action_page="取消租屋套餐-获取套餐列表"
    )


@router.post("/cancel/rent/doback")
async def handle_rent_ware_back(request: DoBackRequest):
    """
    执行取消套餐
    
    处理逻辑：
    1. 从请求参数中获取用户ID
    2. 调用API执行套餐取消
    3. 处理响应数据
    
    Args:
        request: 包含套餐标识和用户认证参数的请求对象
    
    Returns:
        Dict: 包含取消结果的响应对象
    """
    user_id = get_user_id_from_params(request.params)

    data = {
        'package_str': request.package_str,
        'params': f'{request.params}'
    }

    return await make_api_request_with_retry(
        url="https://api.591.com.tw/api/sobot/index?strategy=BackMoney&step_name=doBack&back_type=3",
        data=data,
        user_id=user_id,
        action_page="取消租屋套餐-执行取消"
    )