"""
取消加值服务相关API路由模块
提供获取物件列表、加值服务列表以及执行取消操作的功能
"""

from fastapi import APIRouter
from pydantic import BaseModel
import aiohttp
from logger import log_async_task
import asyncio
from typing import Dict, Any, Optional
from get_user_id import get_user_id_from_params


router = APIRouter()


# 请求模型定义
class WareListRequest(BaseModel):
    """获取物件列表的请求模型"""
    params: str  # 用户认证参数
    back_type: str  # 退回类型


class AdditionalListRequest(BaseModel):
    """获取加值服务列表的请求模型"""
    params: str  # 用户认证参数
    post_id: str  # 物件ID


class DoBackAdditionalRequest(BaseModel):
    """执行取消加值服务的请求模型"""
    params: str  # 用户认证参数
    back_plan_name: str  # 退回方案名称


# HTTP 客户端管理
class HTTPClient:
    """HTTP客户端管理类，维护全局共享的aiohttp会话"""
    _session = None  # 共享的HTTP会话实例

    @classmethod
    async def get_session(cls):
        """获取或创建HTTP会话"""
        if cls._session is None or cls._session.closed:
            cls._session = aiohttp.ClientSession(
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                },
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return cls._session

    @classmethod
    async def close_session(cls):
        """关闭HTTP会话"""
        if cls._session and not cls._session.closed:
            await cls._session.close()
            cls._session = None


# 统一的 API 请求处理
async def make_api_request(
        url: str,
        data: dict,
        user_id: str,
        action_page: str,
        retry_count: int = 0,
        max_retries: int = 2
) -> Dict[str, Any]:
    """
    统一的API请求处理函数
    
    Args:
        url: 请求URL
        data: 请求数据
        user_id: 用户ID
        action_page: 操作页面标识
        retry_count: 当前重试次数
        max_retries: 最大重试次数
    
    Returns:
        Dict[str, Any]: 标准化的响应数据
    """
    try:
        session = await HTTPClient.get_session()
        async with session.post(url, data=data) as response:
            result = await response.json()
            if response.status == 200:
                # 处理重复操作情况
                if result.get("code") == "000001" and "請勿重複操作" in result.get("errorMsg", ""):
                    if retry_count < max_retries:
                        # 记录重试日志
                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="重试",
                            action_desc=f"第{retry_count + 1}次重试"
                        ))
                        await asyncio.sleep(3)
                        return await make_api_request(
                            url=url,
                            data=data,
                            user_id=user_id,
                            action_page=action_page,
                            retry_count=retry_count + 1,
                            max_retries=max_retries
                        )
                    else:
                        print(f"{action_page} - 重试次数已达上限")
                        # 记录失败日志
                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="失败",
                            action_desc="重试3次后仍然失败"
                        ))
                        return {
                            "code": "000001",
                            "message": "請勿重複操作",
                            "data": None
                        }

                # 处理wareList接口的特殊响应
                if "wareList" in url:
                    if result.get("code") == "000000" and result.get("list"):
                        log_desc = "有符合退点条件的物件"
                    elif result.get("code") == "000001" and "未登入" in result.get("errorMsg", ""):
                        log_desc = "未登入会员"
                    else:
                        log_desc = "無符合退點條件的物件"
                else:
                    log_desc = str(result)

                # 记录操作日志
                asyncio.create_task(log_async_task(
                    user_id=user_id,
                    action_page=action_page,
                    action_result="成功" if result.get("code") == "000000" else "失败",
                    action_desc=log_desc
                ))

                # 统一响应格式处理
                response_data = None
                if result.get("code") == "000000":
                    response_data = {}
                    if "wareList" in url:
                        response_data["list"] = result.get("list", [])
                    if "url" in result:
                        response_data["url"] = result.get("url")
                    if "data" in result:
                        response_data.update(result.get("data", {}))

                return {
                    "code": result.get("code"),
                    "message": result.get("errorMsg") if result.get("code") == "000001" else "成功",
                    "data": response_data
                }

            # 处理非200状态码
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page=action_page,
                action_result="失敗",
                action_desc="網路連接超時"
            ))
            return {
                "code": "000001",
                "message": "網路連接超時，請重新整理頁面",
                "data": None
            }

    except Exception as e:
        # 记录异常日志
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page=action_page,
            action_result="失敗",
            action_desc=f"系統錯誤：{str(e)}"
        ))
        return {
            "code": "000001",
            "message": "請求失敗，請稍後重試",
            "data": None
        }


@router.post("/cancel/extend/warelist")
async def handle_ware_list(request: WareListRequest):
    """
    获取可取消的物件列表
    
    Args:
        request: 包含用户参数和退回类型的请求对象
    
    Returns:
        包含物件列表的标准响应
    """
    user_id = get_user_id_from_params(request.params)
    print(user_id)

    data = {
        'params': f'{request.params}',
        'back_type': request.back_type
    }

    return await make_api_request(
        url="https://api.591.com.tw/api/sobot/backMoney/wareList",
        data=data,
        user_id=user_id,
        action_page="取消加值服务-获取物件列表"
    )


@router.post("/cancel/extend/additionallist")
async def handle_additional_list(request: AdditionalListRequest):
    """
    获取指定物件的加值服务列表
    
    Args:
        request: 包含用户参数和物件ID的请求对象
    
    Returns:
        包含加值服务列表的标准响应
    """
    user_id = get_user_id_from_params(request.params)

    data = {
        'params': f'{request.params}',
        'post_id': request.post_id
    }
    print(data)
    try:
        session = await HTTPClient.get_session()
        async with session.post(
            "https://api.591.com.tw/api/sobot/backMoney/additionalList",
            data=data,
            timeout=30
        ) as response:
            result = await response.json()
            print(result)
            log_desc = str(result)
            # 记录操作日志
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page="取消加值服务-获取加值服務列表",
                action_result="成功" if result.get("code") == "000000" else "失败",
                action_desc=log_desc
            ))

            # 处理响应结果
            if result.get("code") == "000000":
                return {
                    "code": "000000",
                    "message": "成功",
                    "data": {
                        "list": result.get("list", [])
                    }
                }
            elif result.get("code") == "000001" and "請勿重複操作" in result.get("errorMsg", ""):
                return {
                    "code": "000002",
                    "message": "正在為您查詢可退點的物件，請稍後...",
                    "data": None
                }
            else:
                return {
                    "code": "000001",
                    "message": result.get("errorMsg", "請求失敗"),
                    "data": None
                }

    except Exception as e:
        # 记录错误日志
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="取消加值服务-获取加值服務列表",
            action_result="失敗",
            action_desc=f"系統錯誤：{str(e)}"
        ))
        return {
            "code": "000001",
            "message": "請求失敗，請稍後重試",
            "data": None
        }


@router.post("/cancel/extend/doback")
async def handle_do_back_additional(request: DoBackAdditionalRequest):
    """
    执行取消加值服务操作
    
    Args:
        request: 包含用户参数和退回方案名称的请求对象
    
    Returns:
        取消操作的结果响应
    """
    user_id = get_user_id_from_params(request.params)

    data = {
        'params': f'{request.params}',
        'back_plan_name': request.back_plan_name
    }

    try:
        session = await HTTPClient.get_session()
        async with session.post(
            "https://api.591.com.tw/api/sobot/backMoney/doBackAdditional",
            data=data,
            timeout=30
        ) as response:
            result = await response.json()
            print(result)
            # 记录操作日志
            log_desc = str(result)
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page="取消加值服务-取消加值服務",
                action_result="成功" if result.get("code") == "000000" else "失败",
                action_desc=log_desc
            ))

            # 处理响应结果
            if result.get("code") == "000000":
                return {
                    "code": "000000",
                    "message": "取消成功",
                    "data": None
                }
            elif result.get("code") == "000001" and "請勿重複操作" in result.get("errorMsg", ""):
                return {
                    "code": "000002",
                    "message": "正在為您處理，請稍後...",
                    "data": None
                }
            else:
                return {
                    "code": "000001",
                    "message": result.get("errorMsg", "取消失敗"),
                    "data": None
                }

    except Exception as e:
        # 记录错误日志
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="取消加值服务-取消加值服務",
            action_result="失敗",
            action_desc=f"系統錯誤：{str(e)}"
        ))
        return {
            "code": "000001",
            "message": "請求失敗，請稍後重試",
            "data": None
        }