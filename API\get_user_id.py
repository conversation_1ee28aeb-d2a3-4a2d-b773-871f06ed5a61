from urllib.parse import parse_qs, unquote
import json
from typing import Optional


def get_user_id_from_params(params: str) -> str:
    """
    从请求参数中提取 user_id

    Args:
        params: 请求参数字符串

    Returns:
        str: 提取到的 user_id，如果提取失败则返回 'unknown'
    """
    try:
        decoded_params = unquote(params)
        json_data = json.loads(decoded_params)
        dialogue_desc = json_data.get('c_cf_dialogueDesc', '')
        params_dict = parse_qs(dialogue_desc)

        return params_dict.get('user_id', ['unknown'])[0]

    except (json.JSONDecodeError, KeyError, AttributeError):
        return 'unknown'