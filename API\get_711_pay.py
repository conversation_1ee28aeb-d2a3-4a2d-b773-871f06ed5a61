"""
7-11繳費代碼相關API路由模組
提供獲取7-11繳費代碼功能，包含以下特性：
1. 參數驗證
2. 請求轉發
3. 錯誤處理
4. 響應格式化
"""

from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Optional
import aiohttp
import json
from logger import log_async_task
from get_user_id import get_user_id_from_params

router = APIRouter()


class ApiResponse(BaseModel):
    """標準API響應模型"""
    code: str  # 響應代碼：000000成功，其他為失敗
    message: str = ""  # 響應消息
    data: Optional[str] = None  # 響應數據，可選


class Get711PayRequest(BaseModel):
    """7-11繳費代碼請求模型"""
    params: str  # 用戶認證參數
    money: str  # 繳費金額

    class Config:
        frozen = True  # 設置為不可變對象


@router.post("/get711pay")
async def handle_get_711_pay(request: Get711PayRequest) -> ApiResponse:
    """
    處理獲取7-11繳費代碼請求

    Args:
        request: 包含用戶認證參數和金額的請求對象

    Returns:
        ApiResponse: 標準響應對象
    """
    user_id = get_user_id_from_params(request.params)

    async def do_request(session):
        """發送請求到591 API"""
        url = 'https://api.591.com.tw/api/sobot/index?strategy=GetPayCode&step=2'
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        data = {
            'params': request.params,
            'money': request.money
        }

        try:
            async with session.post(url, data=data, headers=headers, timeout=200) as response:
                if response.status == 200:
                    return await response.json()
                return None
        except Exception as e:
            return None

    try:
        async with aiohttp.ClientSession() as session:
            result = await do_request(session)

            if not result:
                # 記錄日誌
                await log_async_task(
                    user_id=user_id,
                    action_page="獲取繳費代碼",
                    action_result="失敗",
                    action_desc="請求失敗"
                )
                return ApiResponse(
                    code="000001",
                    message="請求失敗，請稍後重試"
                )

            # 記錄日誌
            await log_async_task(
                user_id=user_id,
                action_page="獲取繳費代碼",
                action_result="成功",
                action_desc=f"結果：{json.dumps(result, ensure_ascii=False)}"
            )

            # 直接返回第三方接口的响应内容
            return ApiResponse(
                code=result.get("code", "000001"),
                message=result.get("errorMsg", "獲取失敗，請稍後重試"),
                data=result.get("errorMsg", "")  # 将完整的HTML消息放在data字段中
            )

    except Exception as e:
        # 記錄錯誤日誌
        await log_async_task(
            user_id=user_id,
            action_page="獲取繳費代碼",
            action_result="失敗",
            action_desc=f"系統錯誤：{str(e)}"
        )
        return ApiResponse(
            code="000001",
            message="系統錯誤，請稍後重試"
        )