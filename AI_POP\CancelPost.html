<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <title>取消出售物件</title>
<style>
/* 基礎樣式設置 */
body {
    overflow: hidden; /* 防止頁面滾動 */
    height: 100vh; /* 設置視窗高度 */
    margin: 0;
    display: flex;
    justify-content: start;
    align-items: flex-start;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* 輸入說明文字樣式 */
.input-instruction {
    margin-top: 30px;
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

/* 搜索區域容器樣式 */
.search-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-top: 50px;
    height: 100%;
}

/* 搜索輸入框樣式 */
#searchBox {
    margin-top: 5px;
    height: 50px;
    padding: 0 15px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 10px;
    width: 200px;
    box-sizing: border-box;
}

/* 取消按鈕樣式 */
#cancelButton {
    padding: 6px 15px;
    font-size: 16px;
    border: 2px solid #FF8000;
    background-color: transparent;
    color: #FF8000;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    align-self: center;
}

/* 取消按鈕懸停效果 */
#cancelButton:hover {
    background-color: #FF8000;
    color: white;
}

/* 結果顯示區域樣式 */
#results {
    overflow-y: auto;
    flex-grow: 1;
    margin-left: 20px;
    width: calc(100% - 400px);
    max-height: calc(100vh - 40px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 10px;
    background-color: #fff;
}

/* 物件卡片樣式 */
.card {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 8px;
    margin: 8px 0;
    transition: transform 0.2s ease;
}

/* 卡片懸停效果 */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 縮圖樣式 */
.thumbnail {
    width: 100px;
    height: 100px;
    margin-right: 10px;
    border-radius: 4px;
    object-fit: cover;
}

/* 詳細信息和標籤樣式 */
.details, .tag {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 標籤樣式 */
.tag {
    color: #FF8000;
    text-align: right;
    margin-top: 10px;
    font-weight: bold;
}

/* 無結果提示樣式 */
.no-results {
    padding: 20px;
    color: #666;
}

/* 消息容器樣式 */
#messageContainer {
    margin-top: 10px;
    padding: 10px;
    background-color: #f1f1f1;
    border-radius: 4px;
    width: 300px;
    box-sizing: border-box;
}

/* 主容器樣式 */
.container {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

/* 自定義鏈接樣式 */
.custom-link {
    text-decoration: none;
    color: blue;
}
</style>

</head>
<body>

<!-- 主要內容區域 -->
<div class="container">
    <!-- 左側搜索區域 -->
    <div class="left-side">
        <H2> AI自助取消出售物件</H2>
        <H3 class="input-instruction"><B>請輸入物件編號或從右邊選擇：</B></H3>
        <p><a href="https://user.591.com.tw/ware/open" target="_blank" class="custom-link">查詢我的物件編號>></a></p>

        <input type="text" id="searchBox" placeholder="請輸入物件編號">
        <button id="cancelButton">確認取消</button>
        <div id="messageContainer"></div>
    </div>

    <!-- 右側結果顯示區域 -->
    <div class="right-side" id="results">
        <div class="card">未獲取到您的登錄狀態，請您登錄賬號後再打開該視窗退點唷^^</div>
    </div>
</div>

<script>
// 頁面加載完成後執行
document.addEventListener('DOMContentLoaded', function() {
    fetchData(); // 獲取初始數據

    // 監聽搜索框輸入事件
    document.getElementById('searchBox').addEventListener('input', function() {
        const searchTerm = this.value.trim();
        const filteredData = searchTerm ? allData.filter(item => item.post_id.includes(searchTerm)) : allData;
        updateUI(filteredData);
    });

    // 監聽取消按鈕點擊事件
    document.getElementById('cancelButton').addEventListener('click', function() {
        const postID = document.getElementById('searchBox').value;
        if (!postID) {
            return;
        }
        cancelPost(postID, window.location.href);
    });
});

// 從服務器獲取物件數據
function fetchData() {
    fetch('https://cs-ai.591.com.tw/api/receive_url', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: window.location.href })
    })
    .then(response => response.json())
    .then(data => {
        allData = data.receivedData || [];
        updateUI(allData);
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// 更新界面顯示
function updateUI(data) {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';
    if (data && data.length > 0) {
        data.forEach(item => createCard(item, resultsContainer));
    } else {
        resultsContainer.innerHTML = '<div class="no-results">暫未查詢到可直接取消的出售物件，您可<span style="color: rgb(225, 60, 57);">獲取物件修改鏈接進行更換</span>或<span style="color: rgb(225, 60, 57);">於左側物件編號輸入框輸入您的物件編號</span>，房吉將為您提交專員申請取消物件^^<p><a href="https://user.591.com.tw/ware/open" target="_blank" class="custom-link">查詢我的物件編號>></a></p><p>服務時間：週一至週日9：00~18：00</p></div>';
    }
}

// 創建物件卡片
function createCard(item, container) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card';
    cardElement.innerHTML = `
        <img src="${item.thumbnail}" alt="Thumbnail" class="thumbnail">
        <div class="content">
            <p><b>${item.post_id}</b></p>
            <p>${item.summary}</p>
            <div class="tag">${item.tag}</div>
        </div>
    `;
    // 點擊卡片時將物件編號填入搜索框
    cardElement.addEventListener('click', () => document.getElementById('searchBox').value = item.post_id);
    container.appendChild(cardElement);
}

// 發送取消物件請求
function cancelPost(postID, url) {
    const cancelButton = document.getElementById('cancelButton');
    const messageContainer = document.getElementById('messageContainer');

    // 顯示處理中的消息並禁用取消按鈕
    messageContainer.innerHTML = '正在為您處理中，請耐心等待^^';
    cancelButton.disabled = true;

    // 發送取消請求
    fetch('https://cs-ai.591.com.tw/api/cancel_post', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ post_id: postID, url: url })
    })
    .then(response => response.json())
    .then(data => {
        messageContainer.innerHTML = '';

        // 顯示返回的消息
        if(data.errorMsg) {
            messageContainer.innerHTML = data.errorMsg;
        }

        // 清空搜索框並重新獲取數據
        document.getElementById('searchBox').value = '';
        setTimeout(fetchData, 1000);
    })
    .catch(error => {
        console.error('Error:', error);
        messageContainer.innerHTML = '<div style="color: red;">操作失敗，請檢查您的賬號登錄狀態</div>';
    })
    .finally(() => {
        // 重新啟用取消按鈕
        cancelButton.disabled = false;
    });
}
</script>

</body>
</html>