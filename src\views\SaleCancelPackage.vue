<template>
  <!-- 頁面主容器 -->
  <div class="page-container">
    <!-- 內容區域：白色背景卡片 -->
    <div class="content">
      <!-- 頁面標題 -->
      <h2 class="title">取消出售套餐</h2>

      <!-- 卡片列表或提示信息區域 -->
      <div class="cards-container">
        <!-- 加載中狀態顯示 -->
        <div v-if="loading" class="message-box loading">
          <el-icon class="loading-icon"><Loading /></el-icon>
          加載中，請稍候...
        </div>

        <!-- 錯誤信息或提示信息顯示 -->
        <div v-else-if="message" class="message-box">
          <div v-html="message"></div>
        </div>

        <!-- 套餐卡片列表 -->
        <div v-else-if="packageList.length" class="cards-grid">
          <!-- 套餐卡片項目 -->
          <div
            v-for="item in packageList"
            :key="item.title"
            class="property-card"
            @click="handleCardClick(item.title)"
          >
            <!-- 卡片內容 -->
            <div class="card-content">
              <div class="package-title">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { Loading } from '@element-plus/icons-vue'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'

/**
 * 类型定义
 */
interface PackageItem {
  title: string      // 套餐标题
}

interface ApiResponse {
  code: string
  message: string
  data?: {
    list?: PackageItem[]
  }
}

/**
 * API 调用封装
 */
const api = {
  // 获取套餐列表
  async getPackageList(params: string) {
    return request<ApiResponse>({
      url: 'api/cancel/sale/packagelist',
      data: { params }
    })
  },

  // 取消套餐
  async doBackPackage(packageStr: string, params: string) {
    return request<ApiResponse>({
      url: 'api/cancel/sale/doback',
      data: {
        package_str: packageStr,
        params
      }
    })
  }
}

/**
 * 状态定义
 */
const message = ref('')                      // 提示信息
const loading = ref(false)                   // 加载状态
const packageList = ref<PackageItem[]>([])   // 套餐列表
const userStore = useUserStore()             // 用户状态

/**
 * 获取套餐列表
 */
const fetchPackageList = async () => {
  loading.value = true
  message.value = ''

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.getPackageList(params)

    // 处理错误
    if (response.code === '000001') {
      message.value = response.message
      packageList.value = []
      return
    }

    // 处理成功响应
    if (response.data?.list?.length) {
      packageList.value = response.data.list
      message.value = ''
    } else {
      message.value = '暫無可取消的出售套餐'
      packageList.value = []
    }
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
    packageList.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 处理卡片点击
 * @param packageTitle - 套餐标题
 */
const handleCardClick = async (packageTitle: string) => {
  loading.value = true
  message.value = ''

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.doBackPackage(packageTitle, params)

    // 处理错误
    if (response.code === '000001') {
      message.value = response.message
      return
    }

    // 处理成功响应
    message.value = response.message || '操作成功'
    packageList.value = []
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
  } finally {
    loading.value = false
  }
}

/**
 * 组件挂载时自动获取套餐列表
 */
onMounted(() =>
    {
      if (userStore.originalData) {
        getDialogueDesc(userStore.originalData);
      }
      fetchPackageList()
    }
)
</script>

<style scoped>
/* 頁面佈局
-------------------------------------------------- */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 消息提示框樣式
-------------------------------------------------- */
.message-box {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  min-height: auto;
  background: #f5f7fa;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  text-align: left;
  padding: 20px;
  line-height: 1.5;
}

/* 消息框內的 HTML 內容樣式 */
.message-box :deep(p) {
  margin: 0;
  padding: 0;
}

.message-box :deep(a) {
  color: #307ae8;
  text-decoration: none;
}

.message-box :deep(a:hover) {
  text-decoration: underline;
}

.message-box :deep(br) {
  content: "";
  display: block;
  margin: 8px 0;
}

/* 加載動畫
-------------------------------------------------- */
.loading {
  color: #409eff;
}

.loading-icon {
  margin-right: 8px;
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

/* 主要內容區域
-------------------------------------------------- */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
  overflow-y: auto;  /* 內容超出時顯示滾動條 */
}

/* 標題樣式 */
.title {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 500;
}

/* 卡片列表容器
-------------------------------------------------- */
.cards-container {
  margin-top: 24px;
  max-height: calc(100vh - 300px);  /* 設置最大高度，超出時顯示滾動條 */
  overflow-y: auto;
}

/* 卡片網格佈局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);  /* 兩列佈局 */
  gap: 20px;
  padding: 4px;
  padding-right: 12px;  /* 為滾動條預留空間 */
}

/* 套餐卡片樣式
-------------------------------------------------- */
.property-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
}

/* 卡片懸停效果 */
.property-card:hover {
  transform: translateY(-2px);
}

.property-card:active {
  transform: scale(0.98);
}

/* 套餐標題樣式 */
.package-title {
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 500;
}

/* 自定義滾動條樣式
-------------------------------------------------- */
.cards-container::-webkit-scrollbar {
  width: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

/* 響應式設計
-------------------------------------------------- */
@media (max-width: 768px) {
  /* 移動端間距調整 */
  .page-container {
    padding: 16px;
  }

  .content {
    padding: 16px;
  }

  /* 移動端單列顯示 */
  .cards-grid {
    grid-template-columns: 1fr;
  }
}
</style>