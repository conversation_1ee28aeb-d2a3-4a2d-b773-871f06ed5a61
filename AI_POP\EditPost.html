<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>獲取特殊修改連結</title>
    <!-- 引入 Roboto 字體 -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500&display=swap" rel="stylesheet">
    <style>
        /* 頁面整體樣式 */
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            overflow: hidden;
        }

        /* 主容器樣式 */
        .container {
            max-width: 600px;
            max-height: 600px;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            text-align: left;
            animation: fadeIn 0.5s ease-in-out;
        }

        /* 淡入動畫效果 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 標題樣式 */
        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }

        /* 文字段落樣式 */
        p {
            margin: 10px 0;
            color: #555;
        }

        /* 輸入框和按鈕共用樣式 */
        input[type="text"], button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }

        /* 按鈕樣式 */
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
        }

        /* 按鈕懸停效果 */
        button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        /* 按鈕點擊效果 */
        button:active {
            background-color: #004494;
        }

        /* 禁用按鈕樣式 */
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        /* 響應消息框樣式 */
        #response-message {
            margin-top: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            border: 1px solid #ddd;
            text-align: left;
            color: #333;
        }

        /* 自定義鏈接樣式 */
        .custom-link {
            text-decoration: none;
            color: #007bff;
            transition: color 0.3s;
        }

        /* 鏈接懸停效果 */
        .custom-link:hover {
            color: #0056b3;
        }

        /* 加載中提示樣式 */
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        /* 紅色高亮文字樣式 */
        .highlight-red {
            color: red;
        }

        /* 彈出警告樣式 */
        .popup-warning {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主要內容容器 -->
    <div class="container">
        <!-- 說明文字 -->
        <p>請在下方輸入您要修改的物件編號，以獲取<span style="color: rgb(225, 60, 57);">可修改任意欄位</span>的連結</p>
        <p><span style="color: rgb(140, 140, 140); background-color: rgb(255, 251, 255); font-size: 15px;">若濫用于更換物件，591有權復原並限制使用權限。</span></p>
        <p><a href="https://user.591.com.tw/ware/open" target="_blank" class="custom-link">查詢物件編號>></a></p>
        
        <!-- 輸入區域 -->
        <input type="text" id="post-id" placeholder="請輸入物件編號，如S12345678">
        <button id="submit-btn">確定</button>
        
        <!-- 響應消息區域 -->
        <div id="response-message"></div>
        <div class="loading" id="loading">正在為您處理，請稍後^^</div>
        <p class="popup-warning" id="popup-warning" style="display: none;">
            您的瀏覽器無法為您自動跳轉物件資料修改頁面，請您點擊鏈接手動進入修改頁面^^
        </p>
    </div>

    <script>
        // 驗證物件編號格式
        function validatePostId(postId) {
            var regex = /^[SsRrDdHh]\d+$/;
            return regex.test(postId);
        }

        // 從URL獲取設備類型
        function getDeviceTypeFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const params = urlParams.get('params');
            if (params) {
                const decodedParams = decodeURIComponent(params);
                const parsedParams = JSON.parse(decodedParams);
                return parsedParams.device;
            }
            return null;
        }

        // 判斷是否需要自動跳轉
        function shouldAutoRedirect(deviceType) {
            const autoRedirectDevices = ['pc']; // 將來可以在此數組中添加更多需要自動跳轉的設備類型
            return autoRedirectDevices.includes(deviceType);
        }

        // 提交按鈕點擊事件處理
        document.getElementById('submit-btn').addEventListener('click', function() {
            const currentUrl = window.location.href;
            const postId = document.getElementById('post-id').value;
            const responseMessage = document.getElementById('response-message');
            const submitBtn = document.getElementById('submit-btn');
            const loading = document.getElementById('loading');
            const popupWarning = document.getElementById('popup-warning');
            const deviceType = getDeviceTypeFromURL();

            // 驗證物件編號
            if (!validatePostId(postId)) {
                responseMessage.innerHTML = '物件編號不正確，請<a href="https://user.591.com.tw/ware/open" target="_blank" class="custom-link">查詢物件編號>></a>';
                return;
            }

            // 發送請求前的準備
            submitBtn.disabled = true;
            responseMessage.innerHTML = '';
            loading.style.display = 'block';

            // 發送API請求
            fetch('https://cs-ai.591.com.tw/api/submit-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: currentUrl,
                    post_id: postId
                }),
            })
            .then(response => response.json())
            .then(data => {
                const retMessage = data.ret_message;
                const linkRegex = /<a href='https:\/\/591\.to(.*?)'/;
                const match = linkRegex.exec(retMessage);
                
                // 處理成功響應
                if (match) {
                    const link = match[1];
                    const fullLink = `https://591.to${link}`;
                    
                    // 根據設備類型處理跳轉
                    if (shouldAutoRedirect(deviceType)) {
                        responseMessage.innerHTML = `
                            已成功為您獲取特殊修改連結，<span class="highlight-red">您可透過該連結修改物件資料或更換物件</span>，連結是：
                            <a href="${fullLink}" target="_blank">${fullLink}</a><br><br>
                        `;
                        const newWindow = window.open(fullLink, '_blank');
                        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                            popupWarning.style.display = 'block';
                        }
                    } else {
                        // 移動設備處理
                        navigator.clipboard.writeText(fullLink).then(() => {
                            responseMessage.innerHTML = `
                                已成功為您獲取特殊修改連結，<span class="highlight-red">您可透過該連結修改物件資料或更換物件</span>，連結是：
                                <a href="${fullLink}" target="_blank">${fullLink}</a><br><br>
                            `;
                        }).catch(err => {
                            responseMessage.innerHTML = `
                                已成功為您獲取特殊修改連結，<span class="highlight-red">您可透過該連結修改物件資料或更換物件</span>，連結是：
                                <a href="${fullLink}" target="_blank">${fullLink}</a><br><br>需請您複製鏈接至手機瀏覽器或透過電腦進入物件資料修改頁面^^
                            `;
                        });
                    }
                } else {
                    responseMessage.innerHTML = retMessage;
                }
                submitBtn.disabled = false;
                loading.style.display = 'none';
            })
            .catch((error) => {
                // 錯誤處理
                responseMessage.innerHTML = '網路超時，請重試！';
                submitBtn.disabled = false;
                loading.style.display = 'none';
            });
        });
    </script>
</body>
</html>
