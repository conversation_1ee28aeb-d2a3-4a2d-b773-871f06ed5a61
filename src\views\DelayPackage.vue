<template>
  <div class="page-container">
    <div v-if="loading" class="result-container">
      <el-row class="full-width" justify="center">
        <el-col :xs="24" :sm="24" :md="20" :lg="16">
          <div class="loading-message">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <span>正在為您處理，請您耐心等待</span>
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-else class="result-container">
      <el-row class="full-width" justify="center">
        <el-col :xs="24" :sm="24" :md="20" :lg="16">
          <div v-if="isRetrying" class="retry-message">
            正在為您處理，請您耐心等待3秒鐘<br><br><br><br>
            溫馨提示：若超過10秒未顯示處理結果，請您重新整理頁面
          </div>

          <el-result
            v-else
            class="custom-result"
            :icon="responseData?.data?.success ? 'success' : 'error'"
            :title="responseData?.data?.success ? '套餐延期成功！' : '套餐延期失敗！'"
          >
            <template #sub-title>
              <div class="custom-sub-title" v-html="formattedSubTitle"></div>
            </template>
          </el-result>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'
import { Loading } from '@element-plus/icons-vue'

// 类型定义
interface ApiResponse {
  code: string
  message: string
  data?: {
    success: boolean
    'sub-title': string
  }
}

// API 封装
const api = {
  async delayPackage(params: string) {
    return request<ApiResponse>({
      url: 'api/delaypackage',
      data: { params }
    })
  }
}

// 状态管理
const userStore = useUserStore()
const loading = ref(false)
const responseData = ref<ApiResponse | null>(null)
const isRetrying = ref(false)

// 计算属性：格式化副标题
const formattedSubTitle = computed(() => {
  if (!responseData.value?.data?.['sub-title']) return ''
  return responseData.value.data['sub-title'].replace(/\n/g, '<br>')
})

// 发送请求函数
const sendRequest = async (isRetry = false) => {
  if (!isRetry) {
    loading.value = true
  }

  try {
    // 使用 getDialogueDesc 处理 params
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.delayPackage(params)

    // 处理重复操作
    if (response.code === '408') {
      isRetrying.value = true
      setTimeout(() => sendRequest(true), 3000)
      return
    }

    // 更新状态
    isRetrying.value = false
    responseData.value = response

  } catch (error) {
    isRetrying.value = false
    responseData.value = {
      code: '000000',
      message: '系統錯誤，請稍後重試',
      data: {
        success: false,
        'sub-title': '系統錯誤，請稍後重試'
      }
    }
  } finally {
    if (!isRetry) {
      loading.value = false
    }
  }
}

// 组件挂载时发送请求
onMounted(() =>
    {
      if (userStore.originalData) {
        getDialogueDesc(userStore.originalData);
      }
      sendRequest()
    }
)
</script>

<style scoped>
/* 頁面佈局
-------------------------------------------------- */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 64px);  /* 減去頁頭高度 */
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

/* 結果展示區域
-------------------------------------------------- */
.result-container {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
}

/* 佈局輔助
-------------------------------------------------- */
.full-width {
  width: 100%;
  margin: 0;
}

/* 重試提示樣式
-------------------------------------------------- */
.retry-message {
  text-align: center;
  font-size: 16px;
  color: #606266;
  padding: 40px 20px;
  margin: 0;
}

/* Element Plus 組件樣式覆蓋
-------------------------------------------------- */
/* 結果組件樣式 */
:deep(.custom-result) {
  padding: 20px 0;
}

/* 副標題樣式 */
:deep(.custom-sub-title) {
  text-align: left;
  max-width: 100%;
  margin: 0 auto;
  color: #343333;
  font-size: 16px;
  line-height: 1.8;
}

/* 額外內容區域對齊 */
:deep(.el-result__extra) {
  text-align: left;
}

/* 成功狀態圖標顏色 */
:deep(.el-result.is-success .el-result__icon) {
  color: #67c23a;
}

/* 錯誤狀態圖標顏色 */
:deep(.el-result.is-error .el-result__icon) {
  color: #f56c6c;
}

/* 標題樣式 */
:deep(.el-result__title) {
  margin-top: 20px;
  font-size: 20px;
  color: #303133;
  font-weight: 500;
}

/* 響應式設計
-------------------------------------------------- */
@media (max-width: 768px) {
  /* 移動端間距調整 */
  .page-container {
    padding: 16px;
  }

  .result-container {
    padding: 8px;
    width: 100%;
  }

  /* 移動端組件樣式調整 */
  :deep(.custom-result) {
    padding: 16px 0;
  }

  :deep(.custom-sub-title) {
    padding: 0;
    max-width: 100%;
    font-size: 14px;
  }

  :deep(.el-result__title) {
    font-size: 18px;
  }

  .retry-message {
    padding: 20px 16px;
    font-size: 14px;
  }
}

/* 加载状态样式 */
.loading-message {
  text-align: center;
  font-size: 16px;
  color: #409EFF;
  padding: 40px 20px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-icon {
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

</style>