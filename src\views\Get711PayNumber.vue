<template>
  <!-- 頁面最外層容器 -->
  <div class="page-container">
    <!-- 內容區域：白色背景卡片 -->
    <div class="content">
      <!-- 頁面標題 -->
      <h2 class="title">獲取7-11繳費代碼</h2>
      
      <!-- 輸入區域：包含輸入框和確認按鈕 -->
      <div class="input-section">
        <!-- Element Plus 輸入框元件
             v-model: 雙向綁定輸入值
             clearable: 可清空輸入
             @input: 輸入事件處理
        -->
        <el-input
          v-model="input"
          style="width: 400px"
          placeholder="請輸入繳費金額（最低儲值100元，最高儲值10000元）"
          clearable
          @input="handleInput"
        />
        
        <!-- 確認按鈕：提交繳費請求
             :loading: 加載狀態控制
        -->
        <el-button
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
        >
          確認
        </el-button>

        <el-button
          @click="handleOpenChargeStandard"
        >
          收費標準
        </el-button>
      </div>

      <!-- 結果展示區域：條件渲染，僅在有消息時顯示 -->
      <div class="result-container" v-if="message">
        <!-- 消息框：使用v-html渲染HTML格式內容 -->
        <div class="message-box" :class="{ error: isError }" v-html="message">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 引入所需的依賴
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../stores/user'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'

// 響應式狀態定義
const input = ref('') // 輸入金額
const message = ref('') // 響應消息
const loading = ref(false) // 加載狀態
const isError = ref(false) // 錯誤狀態
const userStore = useUserStore() // 用戶狀態管理
const handleOpenChargeStandard = () => {
  window.open('https://www.591.com.tw/home/<USER>/chargeIntro/1', '_blank')
}
// 處理輸入：只允許數字
const handleInput = (value: string) => {
  input.value = value.replace(/[^\d]/g, '')
}

// 驗證輸入金額
const validateInput = (): boolean => {
  const amount = Number(input.value)
  // 檢查最低金額
  if (!amount || amount < 100) {
    ElMessage.error('便利超商最低繳費金額為100元')
    return false
  }
  // 檢查最高金額
  if (!amount || amount > 10000) {
    ElMessage.error('便利超商最高繳費金額為10000元')
    return false
  }
  return true
}

// API請求：獲取繳費代碼
const getPayNumber = async (money: string, params: string) => {
  return request({
    url: 'api/get711pay',
    data: {
      money,
      params
    }
  })
}

// 確認按鈕點擊處理
const handleConfirm = async () => {
  // 驗證輸入
  if (!validateInput()) return
  
  // 設置加載狀態
  loading.value = true
  message.value = ''
  isError.value = false

  try {
    // 獲取用戶參數並發送請求
    const params = getDialogueDesc(userStore.originalData)
    const response = await getPayNumber(input.value, params)
    
    // 處理響應結果
    if (response.code === '000000') {
      message.value = response.data || response.message
      isError.value = false
    } else {
      message.value = response.message
      isError.value = true
    }
  } catch (error) {
    // 錯誤處理
    message.value = '请求失败，请稍后重试'
    isError.value = true
  } finally {
    // 結束加載狀態
    loading.value = false
  }
}
</script>

<style scoped>
/* 頁面容器樣式 */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 內容區域樣式 */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
}

/* 標題樣式 */
.title {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 500;
}

/* 輸入區域樣式 */
.input-section {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
}

/* 結果容器樣式 */
.result-container {
  margin-top: 24px;
}

/* 消息框樣式 */
.message-box {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  color: #67c23a;
  font-size: 14px;
  line-height: 1.5;
}

/* 錯誤消息樣式 */
.message-box.error {
  color: #f56c6c;
}

/* 響應式布局樣式 */
@media (max-width: 768px) {
  /* 移動端頁面容器調整 */
  .page-container {
    padding: 16px;
  }

  /* 移動端內容區域調整 */
  .content {
    padding: 16px;
    width: auto;
  }

  /* 移動端輸入區域調整：垂直排列 */
  .input-section {
    flex-direction: column;
    align-items: stretch;
  }

  /* 移動端輸入框寬度調整 */
  .el-input {
    width: 100% !important;
  }
}
</style>