<template>
  <!-- 頁面主容器 -->
  <div class="page-container">
    <!-- 內容區域：白色背景卡片 -->
    <div class="content">
      <!-- 頁面標題 -->
      <h2 class="title">修改物件</h2>

      <!-- 搜索區域 -->
      <div class="search-section">
        <!-- 操作提示 -->
        <p class="description">請輸入物件編號，或從以下列表選擇：</p>

        <!-- 搜索框和按鈕組 -->
        <div class="search-box">
          <!-- Element Plus 輸入框組件 -->
          <el-input
            v-model="input"
            clearable
            placeholder="請輸入物件編號"
            class="search-input"
          />

          <!-- 按鈕組：確認和查詢 -->
          <div class="button-group">
            <el-button type="primary" @click="handleConfirm">確認</el-button>
            <el-button @click="handleQuery">查詢物件編號</el-button>
          </div>
        </div>

        <!-- 卡片列表或提示信息區域 -->
        <div class="cards-container">
          <!-- 加載中狀態 -->
          <div v-if="loading" class="message-box loading">
            <el-icon class="loading-icon"><Loading /></el-icon>
            加載中，請稍候...
          </div>

          <!-- 提示信息顯示 -->
          <div v-else-if="message" class="message-box">
            <div v-html="message"></div>
          </div>

          <!-- 物件卡片列表 -->
          <div v-else-if="propertyList.length" class="cards-grid">
            <!-- 物件卡片項目 -->
            <div
              v-for="item in propertyList"
              :key="item.post_id"
              class="property-card"
              @click="handleCardClick(item.post_id)"
            >
              <!-- 物件圖片 -->
              <div class="card-image">
                <img :src="item.thumbnail" :alt="item.title">
              </div>
              <!-- 物件信息 -->
              <div class="card-content">
                <div class="card-header">
                  <span class="post-id">{{ item.post_id }}</span>
                  <span class="price">{{ item.tag }}</span>
                </div>
                <div class="address">{{ item.summary }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'

// 类型定义
interface PropertyItem {
  title: string
  post_id: string
  thumbnail: string
  url: string
  summary: string
  tag: string
}

interface ApiResponse {
  code: string
  message: string
  data?: {
    list?: PropertyItem[]
    url?: string
  }
}

// API 调用封装
const api = {
  // 获取物件列表
  async getWareList(params: string) {
    return request<ApiResponse>({
      url: 'api/modify/warelist',
      data: { params }
    })
  },

  // 获取修改链接
  async getModifyUrl(postId: string, params: string) {
    return request<ApiResponse>({
      url: 'api/modify/geturl',
      data: {
        post_id: postId,
        params
      }
    })
  }
}

// 状态定义
const message = ref('')
const userStore = useUserStore()
const input = ref('')
const loading = ref(false)
const propertyList = ref<PropertyItem[]>([])
const postIdPattern = /^[SsRr]\d+$/

// 查询按钮处理
const handleQuery = () => {
  window.open('https://user.591.com.tw/ware/open', '_blank')
}

// 获取物件列表
const fetchData = async (isRetry = false) => {
  if (!isRetry) {
    loading.value = true
    message.value = ''
  }

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.getWareList(params)

    // 处理错误
    if (response.code === '000001') {
      message.value = response.message
      propertyList.value = []
      return
    }

    // 处理成功响应
    if (response.data?.list?.length) {
      propertyList.value = response.data.list
      message.value = ''
    } else {
      message.value = '暫未查詢到可修改的物件，請您手動輸入物件編號'
      propertyList.value = []
    }
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
  } finally {
    if (!isRetry) {
      loading.value = false
    }
  }
}

// 确认按钮处理
const handleConfirm = async () => {
  if (!input.value) {
    ElMessage.warning('請輸入物件編號')
    return
  }

  if (!postIdPattern.test(input.value)) {
    ElMessage.error('物件編號格式有誤')
    return
  }

  loading.value = true
  message.value = ''

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.getModifyUrl(input.value, params)

    if (response.code === '000001') {
      message.value = response.message
      return
    }

    // 处理成功响应
    if (response.data?.url) {
      window.open(response.data.url, '_blank')
    }
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
  } finally {
    loading.value = false
  }
}

// 物件卡片点击处理
const handleCardClick = (postId: string) => {
  input.value = postId
}

// 组件挂载
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* 頁面佈局 */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 消息提示框樣式 */
.message-box {
  display: flex;
  align-items: flex-start;
  min-height: auto;
  background: #f5f7fa;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  text-align: left;
  padding: 20px;
  line-height: 1.5;
}

.message-box.loading {
  flex-direction: row;
  align-items: center;
  color: #409eff;
}

.message-box :deep(p) {
  margin: 0;
  padding: 0;
}

.message-box :deep(a) {
  color: #307ae8;
  text-decoration: none;
}

.message-box :deep(a:hover) {
  text-decoration: underline;
}

.message-box :deep(br) {
  content: "";
  display: block;
  margin: 8px 0;
}

/* 加載圖標樣式 */
.loading-icon {
  margin-right: 8px;
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

/* 主要內容區域 */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
  overflow-y: auto;
}

/* 標題和描述文本 */
.title {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 500;
}

.description {
  margin: 0 0 16px 0;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
}

/* 搜索區域樣式 */
.search-section {
  width: 100%;
}

/* 搜索框和按鈕組 */
.search-box {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 24px;
}

.search-input {
  width: 320px;
}

.button-group {
  display: flex;
  gap: 12px;
}

/* 卡片列表區域 */
.cards-container {
  margin-top: 24px;
  height: 300px;
  overflow-y: auto;
}

/* 卡片網格佈局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 4px;
  padding-right: 12px;
}

/* 物件卡片樣式 */
.property-card {
  display: flex;
  background: #fff;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 80px;
  cursor: pointer;
}

.property-card:hover {
  transform: translateY(-2px);
}

.property-card:active {
  transform: scale(0.98);
}

/* 卡片圖片樣式 */
.card-image {
  width: 79px;
  height: 79px;
  flex-shrink: 0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 卡片內容樣式 */
.card-content {
  flex: 1;
  padding: 11px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 卡片頭部樣式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 物件編號和價格樣式 */
.post-id {
  font-weight: 700;
  color: #f56c6c;
  font-size: 14px;
}

.price {
  color: #303133;
  font-weight: 700;
  font-size: 14px;
}

/* 地址文本樣式 */
.address {
  color: #303133;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  font-weight: 700;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .content {
    padding: 16px;
  }

  .search-box {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .button-group {
    width: 100%;
    justify-content: flex-end;
  }

  .cards-grid {
    grid-template-columns: 1fr;
  }
}

/* 自定義滾動條樣式 */
.cards-container::-webkit-scrollbar {
  width: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}
</style>