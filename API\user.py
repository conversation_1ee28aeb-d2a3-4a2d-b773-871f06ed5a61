"""
用戶信息相關API路由模組
提供用戶信息獲取和處理功能，包含以下特性：
1. 用戶信息獲取和驗證
2. 錯誤處理和日誌記錄
3. 自定義錯誤和成功消息模板
"""

from fastapi import APIRouter
from pydantic import BaseModel
from typing import Optional
from urllib.parse import unquote
import json
from logger import log_async_task
import asyncio

router = APIRouter(tags=["user"])


class ApiResponse(BaseModel):
    """標準API響應模型"""
    code: int  # 響應狀態碼
    message: str  # 響應消息
    data: Optional[dict] = None  # 響應數據，可選


# 錯誤消息模板
ERROR_MESSAGE = """<p><strong>親愛的會員：</strong></p><p></p><p>歡迎使用591自助服務系統^^</p><p>遺憾的告訴您，房吉暫未成功獲取您的賬號登錄狀態，請您重新登錄會員賬號後再重新進入此頁，具體步驟如下：</p><p>① <a href="https://user.591.com.tw/transfer/logout?redirect=https://www.591.com.tw" target="_blank">重新登錄</a>591賬號</p><p>② 關閉此頁面</p><p>③ 重新進入此頁面</p>"""


def get_success_message(uname: str) -> str:
    """
    生成成功消息模板
    
    參數:
        uname: 用戶名
        
    返回:
        str: 格式化後的成功消息
    """
    return f"""<p><strong>親愛的{uname}:</strong><br></p><p>歡迎使用591自助服務系統^^</p><p>我們為您提供了<span style="color: rgb(207, 19, 34);">&lt;取消物件&gt;&lt;取消套餐&gt;&lt;取消加值服務&gt;&lt;套餐延期&gt;&lt;修改物件&gt;</span>等多項服務，解決您套餐過期、物件刊登錯誤、加值購買錯誤等問題，若左側服務無法滿足您的服務需求，請您於工作時間聯絡02-55722000或諮詢&lt;<a href="https://www.591.com.tw/home/<USER>" target="_blank">線上客服</a>&gt;</p><p><br></p><p>工作時間：週一至週日9:00~18:00</p>"""


@router.post("/user/info", response_model=ApiResponse)
async def get_user_info(data: str):
    try:
        # 如果數據以 ?data= 開頭，移除這個前綴
        if data.startswith('?data='):
            data = data[6:]

        # URL解碼
        decoded_data = unquote(data)
        user_id = "-1"  # 設置默認值

        # 1. 嘗試處理原有的 params 格式數據
        if decoded_data.startswith('?') and 'params=' in decoded_data:
            try:
                # 提取並解析params參數
                params_start = decoded_data.find('params=') + 7
                params_str = decoded_data[params_start:]
                params_data = json.loads(unquote(params_str))
                # 提取並解析dialogueDesc
                dialogue_desc = params_data.get('c_cf_dialogueDesc', '')
                dialogue_desc = unquote(dialogue_desc)
                # 從dialogueDesc中提取user_id
                if 'user_id=' in dialogue_desc:
                    user_id = dialogue_desc.split('user_id=')[1].split('&')[0]
                    raise ValueError("使用正则解析")  # 如果解析成功就不会执行到这里
            except Exception:
                # 如果原有方式解析失败，尝试使用正则
                import re
                params_match = re.search(r'params=(\{[^}]+\})', decoded_data)
                if params_match:
                    params_str = params_match.group(1)
                    params_data = json.loads(params_str)
                    dialogue_desc = params_data.get('c_cf_dialogueDesc', '')
                    if 'user_id=' in dialogue_desc:
                        user_id = dialogue_desc.split('user_id=')[1].split('&')[0]

        # 2. 處理 data 格式的數據
        elif not decoded_data.startswith('?'):
            # 解析JSON數據
            json_data = json.loads(decoded_data)
            user_id = json_data.get("data", {}).get("partnerid", "-1")

        # 記錄訪問日誌
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="首頁",
            action_result="訪問",
            action_desc=f"用戶:{user_id}訪問首頁"
        ))

        # 根據user_id返回對應消息
        if user_id == "-1":
            return ApiResponse(
                code=200,
                message=ERROR_MESSAGE,
                data={}
            )
        else:
            return ApiResponse(
                code=200,
                message=get_success_message("會員"),
                data={}
            )

    except Exception as e:
        return ApiResponse(
            code=500,
            message=ERROR_MESSAGE,
            data={}
        )