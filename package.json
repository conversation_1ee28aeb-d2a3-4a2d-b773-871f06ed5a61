{"name": "my-nav-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "element-plus": "^2.9.3", "pinia": "^2.3.0", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.6.2", "vite": "^6.0.5", "vue-tsc": "^2.2.0"}}