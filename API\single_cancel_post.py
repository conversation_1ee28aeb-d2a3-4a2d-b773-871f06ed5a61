from fastapi import APIRouter, HTTPException, Form
from pydantic import BaseModel
import json
import aiohttp
import hashlib
import time
from datetime import datetime
from urllib.parse import unquote
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()


class CancelRequest(BaseModel):
    post_id: str
    params: str


@router.post("/single/cancel_post")
async def post_data(
        post_id: str = Form(...),
        params: str = Form(...)
):
    try:
        # 构造请求对象
        request = CancelRequest(post_id=post_id, params=params)

        params_dict = json.loads(request.params)
        c_cf_dialogueDesc_value = unquote(params_dict.get('c_cf_dialogueDesc', ''))
        c_cf_dialogueDesc_dict = dict(item.split('=') for item in c_cf_dialogueDesc_value.split('&'))
        user_id = c_cf_dialogueDesc_dict.get('user_id')

        logger.info(f"会员{user_id}已使用退点自助功能")
        logger.info(f"会员{user_id}已使用退点自助功能")

        # 调用591 API
        url = "https://api.591.com.tw/api/sobot/backMoney/doBack?sence=1"
        data = {
            "post_id": request.post_id,
            "params": json.dumps({"c_cf_dialogueDesc": params_dict.get('c_cf_dialogueDesc', '')})
        }

        headers = {
            "User-Agent": "Mozilla/5.0",
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"Request failed with status code: {response.status}")
                    raise HTTPException(status_code=500, detail="請求失敗")

                response_data = await response.json()

                if any(msg in response_data.get("errorMsg", "") for msg in
                       ["取消成功！", "未登入會員或登入狀態", "親愛的會員", "抱歉"]):
                    return {"ret_code": 0, "ret_message": response_data["errorMsg"]}

                # 创建智齿工单
                appid = "de142f75652241d3ae8de708da162380"
                app_key = "Q6R65n268IR0"
                create_time = str(int(time.time()))
                sign = hashlib.md5((appid + create_time + app_key).encode()).hexdigest()

                # 获取token
                token_url = f"https://www.soboten.com/api/get_token?appid={appid}&create_time={create_time}&sign={sign}"
                async with session.get(token_url) as token_response:
                    token_data = await token_response.json()
                    token = token_data['item']['token']

                # 创建工单
                ticket_data = {
                    "companyid": "3e8cbefa30da455eb3039c3f2d19a8f0",
                    "ticket_title": f"【取消退點（單輪）】會員ID：{user_id}",
                    "userid": user_id,
                    "partnerid": user_id,
                    "ticket_content": f"會員ID：<a href=\"https://www.591.com.tw/admin.php?module=userInfo&action=list&id={user_id}&incall=1&ln=home&sn=index\" target=\"_blank\"> {user_id}  </a>\n物件編號： {request.post_id}\n賬號消費明細：<a href=\"https://www.591.com.tw/admin.php?module=userInfo&action=moneyLog&id={user_id}&ln=home&sn=index\" target=\"_blank\">查看</a>\n發送手機簡訊：<a href=\"https://www.591.com.tw/admin.php?module=wrap_up&action=mobileList&ln=home&sn=index \" target=\"_blank\">去發送</a>\n\n<p>簡訊模板：</p><p><b>已取消:</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服中心已為您處理，並退回1筆套餐數至您賬戶內，廣告資料隨之刪除，請您方便時確認唷~【591感謝您】\n\n<b>刊登较久不可取消但可更换：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，因刊登時間較長，客服中心暫無法為您取消，但可幫您更換物件刊登，如需更換請您透過鏈接[&nbsp;&nbsp;&nbsp;]修改物件任意欄位，若有疑問可聯絡客服中心02-55722000【591感謝您】\n<span style=\"color: rgb(140, 140, 140);\">獲取修改鏈接：</span><a href=\"https://admin.591.com.tw/admin/udesk/change-url\" target=\"_blank\"><span style=\"color: rgb(140, 140, 140);\">https://admin.591.com.tw/admin/udesk/change-url</span></a>\n\n<b>刊登较久不可取消也不可更换：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，由於物件刊登時間較長，客服中心暫無法為您處理唷，如有疑問可聯絡客服中心02-55722000【591感謝您】\n\n<b>无法搜寻到广告不处理：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服至您賬號內無法搜尋到此廣告，無法為您取消，請您確認編號是否有提交錯誤，不便之處，請見諒~【591感謝您】\n\n<b>取消物件精选过久不取消：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服現已為您處理，廣告已取消，並退回一筆套餐至您賬號內，廣告資料隨之刪除，但由於精選服務已過取消期限，暫未處理唷，請您方便時確認看看~【591感謝您】\n\n<b>下架租屋物件恢复栏位:</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服中心已為您處理，廣告已下架，並將櫥窗欄位恢復至未使用，請您方便時再確認下唷~【591感謝您】\n\n<b>提交错误资讯不处理：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服查詢物件編號有誤，無法為您取消，請您確認編號是否有提交錯誤，不便之處，請見諒~【591感謝您】",
                    "ticket_status": "0",
                    "ticket_level": "3",
                    "create_agentid": "0a37c8156d094311890e48fbc06501c4",
                    "create_agent_name": "ming",
                    "ticket_typeid": "b9652f4116f848deb263e49de21b098b",
                    "ticket_from": "0"
                }

                ticket_headers = {
                    "Content-Type": "application/json",
                    "token": token
                }

                async with session.post('https://www.soboten.com/api/ws/5/ticket/save_user_ticket',
                                        json=ticket_data,
                                        headers=ticket_headers) as ticket_response:
                    if ticket_response.status != 200:
                        logger.error("創建工單失敗")
                        raise HTTPException(status_code=500, detail="創建工單失敗")

                hour = datetime.now().hour
                if 0 <= hour < 6 or 6 <= hour < 9:
                    ret_message = "經查詢該筆物件不符合網站退點規則，現已爲您提交專員審核。專員將在今天11:00前處理"
                elif 9 <= hour < 16:
                    ret_message = "經查詢該筆物件不符合網站退點規則，現已爲您提交專員審核。專員將在2小時內處理"
                elif 16 <= hour < 18:
                    ret_message = "經查詢該筆物件不符合網站退點規則，現已爲您提交專員審核。專員將在18:00前處理"
                else:
                    ret_message = "經查詢該筆物件不符合網站退點規則，現已爲您提交專員審核。專員將在明天11:00前處理"

                logger.info(f"工單創建成功: post_id={request.post_id}, user_id={user_id}")
                return {"ret_code": 0, "ret_message": ret_message}

    except Exception as e:
        logger.error(f"取消物件失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))