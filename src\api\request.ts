import axios from 'axios'
import type { AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios'
import { ElMessage } from 'element-plus'

// 重试请求函数
const retryRequest = async (config: AxiosRequestConfig) => {
  try {
    // 等待3秒后重试
    await new Promise(resolve => setTimeout(resolve, 3000))
    return await axios(config)
  } catch (error) {
    return Promise.reject(error)
  }
}

// 显示错误信息函数
const showError = (message: string) => {
  ElMessage.error(message)
}

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
  method: 'post',
  headers: {
    'Content-Type': 'application/json'
  }
})

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message, data } = response.data

    switch (code) {
      case 200:
        return response.data

      case 408: // 超时,需要重试
        return retryRequest(response.config)

      default:
        // 其他所有情况都直接返回原始响应数据
        return response.data

    }
  },
  (error: AxiosError) => {
    showError('網路錯誤')
    return Promise.reject(error)
  }
)

export default request

// 期待後端返回的數據格式：
// {
//   "code": number,    // 状态码：200成功；408超時，3s後重新請求；500服務器錯誤
//   "message": string, // 提示信息：errorMsg或自定義回復
//   "data": any       // 业务数据
// }