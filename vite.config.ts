import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 5174,  // 訪問端口
    watch: {
      ignored: [
        '**/API/**',
        '**/venv/**',
        '**/__pycache__/**',
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**'
      ]
    },
    hmr: {
      protocol: 'ws',
      host: 'localhost',
      port: 5174,
      clientPort: 5174
    }
  }
})