<template>
  <!-- 頁面最外層容器：提供整體佈局和間距 -->
  <div class="page-container">
    <!-- 內容區域：包含白色背景和陰影效果的主要內容區 -->
    <div class="content">
      <!-- 頁面標題 -->
      <h2 class="title">清空價格波動</h2>

      <!-- 搜索區域：包含物件列表 -->
      <div class="search-section">
        <div class="cards-container">
          <!-- 加載中狀態顯示 -->
          <div v-if="loading" class="message-box loading">
            <!-- Element Plus 加載圖標 -->
            <el-icon class="loading-icon"><Loading /></el-icon>
            加載中，請稍候...
          </div>

          <!-- 錯誤信息顯示區域 -->
          <div v-else-if="message" class="message-box">
            <!-- 使用 v-html 渲染可能包含 HTML 標籤的錯誤信息 -->
            <div v-html="message"></div>
            <!-- 返回按鈕 -->
            <div v-if="showBackButton" class="button-container">
              <el-button type="primary" @click="handleRefresh">返回</el-button>
            </div>
          </div>

          <!-- 物件卡片列表：當有數據時顯示 -->
          <div v-else-if="propertyList.length" class="cards-grid">
            <!-- 物件卡片項目：使用 v-for 遍歷物件列表 -->
            <div
              v-for="item in propertyList"
              :key="item.post_id"
              class="property-card"
              @click="handleCardClick(item.post_id)"
            >
              <!-- 物件圖片容器 -->
              <div class="card-image">
                <!-- 物件縮略圖 -->
                <img :src="item.thumbnail" :alt="item.title">
              </div>
              <!-- 物件信息容器 -->
              <div class="card-content">
                <!-- 卡片頭部：顯示物件編號和標籤 -->
                <div class="card-header">
                  <span class="post-id">{{ item.post_id }}</span>
                  <span class="price">{{ item.tag }}</span>
                </div>
                <div class="address">{{ item.summary }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'

// 类型定义
interface PropertyItem {
  title: string
  post_id: string
  thumbnail: string
  url: string
  summary: string
  tag: string
}

interface ApiResponse {
  code: string
  message: string
  data?: {
    list?: PropertyItem[]
    api1_error?: boolean
    api2_error?: boolean
  }
}

// API 调用封装
const api = {
  // 获取物件列表
  async getWareList(params: string) {
    return request<ApiResponse>({
      url: 'api/DeletePriceFluctuations/list',
      data: { params }
    })
  },

  // 清空價格波動
  async cancelWare(postId: string, params: string) {
    return request<ApiResponse>({
      url: 'api/DeletePriceFluctuations/Clear',
      data: {
        post_id: postId,
        params
      }
    })
  }
}

// 状态定义
const showBackButton = ref(false)
const message = ref('')
const userStore = useUserStore()
const loading = ref(false)
const propertyList = ref<PropertyItem[]>([])

// 修改物件卡片点击处理函数
const handleCardClick = async (postId: string) => {
  loading.value = true
  message.value = ''
  showBackButton.value = false

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.cancelWare(postId, params)

    message.value = response.message
    showBackButton.value = true
    propertyList.value = []
    // 清空成功后重新获取列表
    setTimeout(() => fetchData(), 3000)
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
    showBackButton.value = true
  } finally {
    loading.value = false
  }
}

// 页面刷新处理
const handleRefresh = () => {
  showBackButton.value = false
  message.value = ''
  propertyList.value = []
  fetchData()
}


// 获取物件列表
const fetchData = async (isRetry = false) => {
  if (!isRetry) {
    loading.value = true
    message.value = ''
  }

  try {
    const params = getDialogueDesc(userStore.originalData)
    const response = await api.getWareList(params)

    // 处理临时错误（需要重试）
    if (response.code === '000002') {
      message.value = response.message
      propertyList.value = []
      setTimeout(() => fetchData(true), 3000)
      return
    }

    // 处理永久错误
    if (response.code === '000001') {
      message.value = response.message
      propertyList.value = []
      return
    }

    // 处理成功响应
    if (response.data?.list?.length) {
      propertyList.value = response.data.list
      message.value = ''
    } else {
      message.value = '暫未查詢到可退點的物件，請您手動輸入物件編號'
      propertyList.value = []
    }
  } catch (error) {
    message.value = '請求失敗，請稍後重試'
  } finally {
    if (!isRetry) {
      loading.value = false
    }
  }
}

// 组件挂载
onMounted(() =>
    {
      if (userStore.originalData) {
        getDialogueDesc(userStore.originalData);
      }
      fetchData()
    }
)
</script>

<style scoped>
/* 頁面整體佈局
-------------------------------------------------- */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;  /* 居中顯示 */
}

/* 消息提示框樣式
-------------------------------------------------- */
.message-box {
  display: flex;
  flex-direction: column;  /* 保持垂直排列，用于错误信息和按钮 */
  align-items: flex-start;
  min-height: auto;
  background: #f5f7fa;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  text-align: left;
  padding: 20px;
  line-height: 1.5;
}

.message-box.vertical {
  flex-direction: column;
  align-items: flex-start;
}

/* 加载状态特殊处理 */
.message-box.loading {
  flex-direction: row;  /* 加载状态使用水平排列 */
  align-items: center;  /* 垂直居中对齐 */
  color: #409eff;
}

/* 消息框內部元素樣式 */
.message-box :deep(p) {
  margin: 0;
  padding: 0;
}

.message-box :deep(a) {
  color: #307ae8;
  text-decoration: none;
}

.message-box :deep(a:hover) {
  text-decoration: underline;
}

.message-box :deep(br) {
  content: "";
  display: block;
  margin: 8px 0;  /* 設置段落間距 */
}

/* 加載狀態樣式
-------------------------------------------------- */
.loading {
  color: #409eff;
}

/* 按鈕容器樣式 */
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;  /*顶部分隔线 */
}

/* 加載圖標樣式 */
.loading-icon {
  margin-right: 8px;
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

/* 主要內容區域樣式
-------------------------------------------------- */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
  overflow-y: auto;
}

/* 標題和描述文本樣式
-------------------------------------------------- */
.title {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 500;
}

.description {
  margin: 0 0 16px 0;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
}

/* 搜索區域樣式
-------------------------------------------------- */
.search-section {
  width: 100%;
}

/* 搜索框和按鈕組樣式 */
.search-box {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 24px;
}

.search-input {
  width: 320px;
}

.button-group {
  display: flex;
  gap: 12px;
}

/* 卡片列表區域樣式
-------------------------------------------------- */
.cards-container {
  margin-top: 24px;
  height: 300px;
  overflow-y: auto;  /* 啟用垂直滾動 */
}

/* 卡片網格佈局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 4px;
  padding-right: 12px;
  transition: opacity 0.3s ease;
}

/* 物件卡片樣式 */
.property-card {
  display: flex;
  background: #fff;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 80px;
  cursor: pointer;
}

/* 卡片懸停和點擊效果 */
.property-card:hover {
  transform: translateY(-2px);
}

.property-card:active {
  transform: scale(0.98);
}

/* 卡片圖片樣式 */
.card-image {
  width: 79px;
  height: 79px;
  flex-shrink: 0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 卡片內容樣式 */
.card-content {
  flex: 1;
  padding: 11px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 卡片頭部樣式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 物件編號和價格樣式 */
.post-id {
  font-weight: 700;
  color: #f56c6c;
  font-size: 14px;
}

.price {
  color: #303133;
  font-weight: 700;
  font-size: 14px;
}

/* 地址文本樣式 */
.address {
  color: #303133;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  font-weight: 700;
  -webkit-line-clamp: 2;  /* 限制文本行數 */
  -webkit-box-orient: vertical;
}

/* 響應式設計
-------------------------------------------------- */
@media (max-width: 768px) {
  /* 移動端佈局調整 */
  .page-container {
    padding: 16px;
  }

  .content {
    padding: 16px;
  }

  /* 搜索框垂直排列 */
  .search-box {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .button-group {
    width: 100%;
    justify-content: flex-end;
  }

  /* 卡片單列顯示 */
  .cards-grid {
    grid-template-columns: 1fr;
  }
}

/* 自定義滾動條樣式
-------------------------------------------------- */
.cards-container::-webkit-scrollbar {
  width: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}
</style>