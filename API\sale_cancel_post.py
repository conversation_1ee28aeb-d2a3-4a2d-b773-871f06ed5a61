# -*- coding: utf-8 -*-

"""
此文件用於處理591網站的出售物件取消功能
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy import Column, String, DateTime, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os
from dotenv import load_dotenv
import aiohttp
import json
import hashlib
import time
from urllib.parse import urlparse, parse_qs, unquote
import logging

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sale_cancel.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 加載環境變量
load_dotenv()

# 數據庫配置
DATABASE_URL = (
    f"mysql+aiomysql://{os.getenv('DB_USER')}:{os.getenv('DB_PASSWORD')}@"
    f"{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
    f"?charset=utf8mb4"
)

# 創建異步引擎
engine = create_async_engine(
    DATABASE_URL,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800
)

# 創建異步會話
async_session = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()

class PopLog(Base):
    __tablename__ = "poplog"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(50), nullable=False, comment="用戶ID")
    time = Column(DateTime, nullable=False, comment="操作時間")
    post_id = Column(String(50), nullable=False, comment="物件編號")
    type = Column(String(20), nullable=False, comment="操作類型")
    response_result = Column(String(500), nullable=False, comment="API響應結果")
    result = Column(String(50), nullable=False, comment="操作結果")

async def init_db():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# 創建路由
router = APIRouter()

class URLRequest(BaseModel):
    url: str

class CancelRequest(BaseModel):
    post_id: str
    url: str

async def get_db():
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()

async def insert_to_database(db: AsyncSession, user_id: str, post_id: str, response_result: str) -> None:
    try:
        current_time = datetime.now()
        type = "退點"

        if "取消成功" in response_result:
            result = "取消成功"
        elif "未登入會員" in response_result:
            result = "未登入會員"
        elif "親愛的會員" in response_result:
            result = "取消成功"
        else:
            result = "取消失敗"

        log_entry = PopLog(
            user_id=user_id,
            time=current_time,
            post_id=post_id,
            type=type,
            response_result=response_result,
            result=result
        )

        db.add(log_entry)
        await db.commit()
        logger.info(f"數據插入成功: user_id={user_id}, post_id={post_id}")

    except Exception as e:
        await db.rollback()
        logger.error(f"數據庫操作失敗: {str(e)}")
        raise HTTPException(status_code=500, detail="數據庫操作失敗")

@router.post("/receive_url")
async def receive_url(request: URLRequest, db: AsyncSession = Depends(get_db)):
    try:
        parsed_url = urlparse(request.url)
        query_params = parse_qs(parsed_url.query)

        params_value = query_params.get('params', [None])[0]
        if params_value and params_value.endswith('\"'):
            params_value = params_value[:-1]

        try:
            decoded_json = json.loads(params_value)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="請從AI智慧客服開啓該視窗")

        c_cf_dialogueDesc_value = "{\"c_cf_dialogueDesc\":\"" + decoded_json.get('c_cf_dialogueDesc', '') + "\"}"

        url = "https://api.591.com.tw/api/sobot/backMoney/wareList"
        data = {"back_type": "sale_ware", "params": c_cf_dialogueDesc_value}
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "Origin": "https://www.591.com.tw",
            "Referer": "https://www.591.com.tw/",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers) as response:
                response_data = await response.json()
                list_items = response_data.get("list", [])

        filtered_data = [{
            "post_id": item.get("post_id"),
            "thumbnail": item.get("thumbnail"),
            "summary": item.get("summary"),
            "tag": item.get("tag")
        } for item in list_items]

        return {"message": "Data received successfully!", "receivedData": filtered_data}

    except Exception as e:
        logger.error(f"獲取物件列表失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/cancel_post")
async def cancel_post(request: CancelRequest, db: AsyncSession = Depends(get_db)):
    try:
        parsed_url = urlparse(request.url)
        query_params = parse_qs(parsed_url.query)

        params_value = query_params.get('params', [None])[0]
        if params_value and params_value.endswith('\"'):
            params_value = params_value[:-1]

        try:
            decoded_json = json.loads(params_value)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="請從AI智慧客服開啓該視窗")

        c_cf_dialogueDesc_value = "{\"c_cf_dialogueDesc\":\"" + decoded_json.get('c_cf_dialogueDesc', '') + "\"}"

        url = "https://api.591.com.tw/api/sobot/backMoney/doBack"
        data = {"post_id": request.post_id, "params": c_cf_dialogueDesc_value}

        params_json = data['params']
        params = json.loads(params_json)
        c_cf_dialogueDesc = unquote(params['c_cf_dialogueDesc'])
        params_dict = parse_qs(c_cf_dialogueDesc)
        user_id = params_dict['user_id'][0]

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers) as response:
                response_data = await response.json()
                error_msg = response_data.get("errorMsg", "")

        await insert_to_database(db, user_id, request.post_id, error_msg)

        if "取消成功" in error_msg or "未登入會員" in error_msg or "親愛的會員" in error_msg or "抱歉" in error_msg:
            logger.info(f"物件取消成功: post_id={request.post_id}, user_id={user_id}")
            return {"status": error_msg, "message": error_msg, "errorMsg": error_msg}

        url_next = 'https://www.soboten.com/api/ws/5/ticket/save_user_ticket'
        appid = os.getenv("APPID")
        app_key = os.getenv("APP_KEY")
        create_time = str(int(time.time()))

        sign = hashlib.md5((appid + create_time + app_key).encode()).hexdigest()

        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"https://www.soboten.com/api/get_token?appid={appid}&create_time={create_time}&sign={sign}"
            ) as response:
                token_data = await response.json()
                token = token_data['item']['token']

        headers = {
            'Content-Type': 'application/json',
            'token': token
        }

        ticket_data = {
            "companyid": "3e8cbefa30da455eb3039c3f2d19a8f0",
            "ticket_title": f"【取消退點（彈窗）】會員ID：{user_id}",
            "userid": user_id,
            "partnerid": user_id,
            "ticket_content": f"會員ID：<a href=\"https://www.591.com.tw/admin.php?module=userInfo&action=list&id={user_id}&incall=1&ln=home&sn=index\" target=\"_blank\"> {user_id}  </a>\n物件編號： {request.post_id}\n賬號消費明細：<a href=\"https://www.591.com.tw/admin.php?module=userInfo&action=moneyLog&id={user_id}&ln=home&sn=index\" target=\"_blank\">查看</a>\n發送手機簡訊：<a href=\"https://www.591.com.tw/admin.php?module=wrap_up&action=mobileList&ln=home&sn=index \" target=\"_blank\">去發送</a>\n\n<p>簡訊模板：</p><p><b>已取消:</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服中心已為您處理，並退回1筆套餐數至您賬戶內，廣告資料隨之刪除，請您方便時確認唷~【591感謝您】\n\n<b>刊登较久不可取消但可更换：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，因刊登時間較長，客服中心暫無法為您取消，但可幫您更換物件刊登，如需更換請您透過鏈接[&nbsp;&nbsp;&nbsp;]修改物件任意欄位，若有疑問可聯絡客服中心02-55722000【591感謝您】\n<span style=\"color: rgb(140, 140, 140);\">獲取修改鏈接：</span><a href=\"https://admin.591.com.tw/admin/udesk/change-url\" target=\"_blank\"><span style=\"color: rgb(140, 140, 140);\">https://admin.591.com.tw/admin/udesk/change-url</span></a>\n\n<b>刊登较久不可取消也不可更换：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，由於物件刊登時間較長，客服中心暫無法為您處理唷，如有疑問可聯絡客服中心02-55722000【591感謝您】\n\n<b>无法搜寻到广告不处理：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服至您賬號內無法搜尋到此廣告，無法為您取消，請您確認編號是否有提交錯誤，不便之處，請見諒~【591感謝您】\n\n<b>取消物件精选过久不取消：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服現已為您處理，廣告已取消，並退回一筆套餐至您賬號內，廣告資料隨之刪除，但由於精選服務已過取消期限，暫未處理唷，請您方便時確認看看~【591感謝您】\n\n<b>下架租屋物件恢复栏位:</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服中心已為您處理，廣告已下架，並將櫥窗欄位恢復至未使用，請您方便時再確認下唷~【591感謝您】\n\n<b>提交错误资讯不处理：</b>\n親愛的會員：您於AI自助系統申請取消物件{request.post_id}的需求，客服查詢物件編號有誤，無法為您取消，請您確認編號是否有提交錯誤，不便之處，請見諒~【591感謝您】",
            "ticket_status": "0",
            "ticket_level": "3",
            "create_agentid": "0a37c8156d094311890e48fbc06501c4",
            "create_agent_name": "ming",
            "ticket_typeid": "b9652f4116f848deb263e49de21b098b",
            "ticket_from": "0"
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url_next, json=ticket_data, headers=headers) as response:
                if response.status != 200:
                    logger.error(f"創建工單失敗: {await response.text()}")
                    raise HTTPException(status_code=500, detail="創建工單失敗")

        current_time = datetime.now()
        hour = current_time.hour

        if 0 <= hour < 6:
            ret_message = "經查詢該筆物件不符合網站退點規則，<span style=\"color: rgb(225, 60, 57);\">現已爲您提交專員審核。</span>專員將在<span style=\"color: rgb(225, 60, 57);\">今天11:00前</span>將處理結果透過手機簡訊發送至物件上的手機號碼中，請您耐心等待^^\n\n\n\n溫馨提示：若您需撤銷取消申請，請您及時聯絡客服說明唷"
        elif 6 <= hour < 9:
            ret_message = "經查詢該筆物件不符合網站退點規則，<span style=\"color: rgb(225, 60, 57);\">現已爲您提交專員審核。</span>專員將在<span style=\"color: rgb(225, 60, 57);\">今天11:00前</span>將處理結果透過手機簡訊發送至物件上的手機號碼中，請您耐心等待^^\n\n\n\n溫馨提示：若您需撤銷取消申請，請您及時聯絡客服說明唷"
        elif 9 <= hour < 16:
            ret_message = "經查詢該筆物件不符合網站退點規則，<span style=\"color: rgb(225, 60, 57);\">現已爲您提交專員審核。</span>專員將在<span style=\"color: rgb(225, 60, 57);\">2小時內</span>將處理結果透過手機簡訊發送至物件上的手機號碼中，請您耐心等待^^\n\n\n\n溫馨提示：若您需撤銷取消申請，請您及時聯絡客服說明唷"
        elif 16 <= hour < 18:
            ret_message = "經查詢該筆物件不符合網站退點規則，<span style=\"color: rgb(225, 60, 57);\">現已爲您提交專員審核。</span>專員將在<span style=\"color: rgb(225, 60, 57);\">18:00前</span>將處理結果透過手機簡訊發送至物件上的手機號碼中，請您耐心等待^^\n\n\n\n溫馨提示：若您需撤銷取消申請，請您及時聯絡客服說明唷"
        else:
            ret_message = "經查詢該筆物件不符合網站退點規則，<span style=\"color: rgb(225, 60, 57);\">現已爲您提交專員審核。</span>專員將在<span style=\"color: rgb(225, 60, 57);\">明天11:00前</span>將處理結果透過手機簡訊發送至物件上的手機號碼中，請您耐心等待^^\n\n\n\n溫馨提示：若您需撤銷取消申請，請您及時聯絡客服說明唷"

        logger.info(f"工單創建成功: post_id={request.post_id}, user_id={user_id}")
        return {"status": ret_message, "message": ret_message, "errorMsg": ret_message}

    except Exception as e:
        logger.error(f"取消物件失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))