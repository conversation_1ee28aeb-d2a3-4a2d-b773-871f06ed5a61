from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from delay_package import router as delay_router
from modify_post import router as modify_router
from cancel_post import router as cancel_router
from sale_cancel_package import router as sale_cancel_package_router
from rent_cancel_package import router as rent_cancel_package_router
from cancel_extend import router as cancel_extend_router
from user import router as user_router
from health_router import health_router as health
from line_database import line_database_router as line_database
from sale_cancel_post import router as sale_cancel_post_router
from sale_modify_post import router as sale_modify_post
from single_cancel_post import router as single_cancel_post
from get_711_pay import router as get_711_pay_router
from Delete_price_fluctuations import router as delete_price_fluctuations_router

import os
from fastapi.staticfiles import StaticFiles

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="591 客服 AI API",
        version="1.0.0",
        description="591客服AI系統API文檔",
        routes=app.routes,
        openapi_version="3.1.0"
    )

    openapi_schema["servers"] = [
        {"url": "https://cs-ai.debug.591.com.tw", "description": "Debug环境"},
        {"url": "https://cs-ai.591.com.tw", "description": "生产环境"}
    ]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# 创建 FastAPI 应用、配置文档
app = FastAPI(
    title="591 客服 AI API",
    description="591客服AI系統API文檔",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 使用自定义OpenAPI schema
app.openapi = custom_openapi

# 配置静态文件
app.mount("/Line/image", StaticFiles(directory="/home/<USER>/www/Line/image"), name="line_image")
app.mount("/AI_POP", StaticFiles(directory="/home/<USER>/www/AI_POP"), name="ai_pop")
# app.mount("/Line/image", StaticFiles(directory=r"C:\Users\<USER>\Desktop\main\T5-AI-master\Line\image"), name="line_image")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 註冊路由
app.include_router(delay_router, prefix="/api", tags=["套餐延期"])
app.include_router(modify_router, prefix="/api", tags=["修改物件"])
app.include_router(cancel_router, prefix="/api", tags=["取消退點"])
app.include_router(sale_cancel_package_router, prefix="/api", tags=["取消出售套餐"])
app.include_router(rent_cancel_package_router, prefix="/api", tags=["取消出租套餐"])
app.include_router(cancel_extend_router, prefix="/api", tags=["取消加值"])
app.include_router(user_router, prefix="/api", tags=["首頁返回會員信息"])
app.include_router(health, prefix="/api", tags=["健康檢查"])
app.include_router(line_database, prefix="/api", tags=["Line QR Code"])
app.include_router(sale_cancel_post_router, prefix="/api", tags=["出售物件取消（AI彈窗）"])
app.include_router(sale_modify_post, prefix="/api", tags=["修改出售物件（AI彈窗）"])
app.include_router(single_cancel_post, prefix="/api", tags=["取消出售物件（AI單輪）"])
app.include_router(get_711_pay_router, prefix="/api", tags=["獲取7-11繳費代碼"])
app.include_router(delete_price_fluctuations_router, prefix="/api", tags=["獲取7-11繳費代碼"])