from fastapi import APIRouter, HTTPException
from datetime import datetime
import psutil
import platform
from typing import Dict

# 创建路由
health_router = APIRouter()

@health_router.get("/health")
async def health_check() -> Dict:
    """
    健康检查接口
    返回服务器状态信息
    """
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "service": "T5-AI",
            "version": "1.0.0",
            "system_info": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "platform": platform.platform(),
                "python_version": platform.python_version()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))