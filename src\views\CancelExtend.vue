// 前端代碼
<template>
  <!-- 頁面最外層容器 -->
  <div class="page-container">
    <!-- 主要內容區域：白色背景卡片 -->
    <div class="content">
      <!-- 頁面標題 -->
      <h2 class="title">取消加值服務</h2>

      <!-- 操作說明和提示信息 -->
      <p class="description">
        <!-- 流程說明 -->
        取消加值服務流程：①選擇物件 → ②選擇加值服務 → ③完成取消<br><br>
        <!-- 重要提示信息 -->
        溫馨提示：<br>
        1、當前僅可取消出售物件的置頂及精選競價<br>
        2、若加值服務未扣費，請至會員中心取消續購
      </p>

      <!-- 卡片展示區域：包含加載狀態、錯誤提示、物件列表和加值服務列表 -->
      <div class="cards-container">
        <!-- 加載中狀態顯示 -->
        <div v-if="loading" class="message-box loading">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加載中，請稍候...</span>
        </div>

        <!-- 錯誤信息顯示區域 -->
        <div v-else-if="message" class="message-box">
          <!-- 使用 v-html 渲染可能包含 HTML 標籤的錯誤信息 -->
          <div v-html="message"></div>
        </div>

        <!-- 物件卡片列表：當有數據且不顯示加值服務列表時顯示 -->
        <div v-else-if="postList.length && !showAdditionalList" class="cards-grid">
          <!-- 物件卡片項目 -->
          <div
            v-for="item in postList"
            :key="item.post_id"
            class="property-card"
            @click="handlePostClick(item.post_id)"
          >
            <!-- 物件圖片容器 -->
            <div class="card-image">
              <img :src="item.thumbnail" :alt="item.title">
            </div>
            <!-- 物件信息容器 -->
            <div class="card-content">
              <!-- 卡片頭部：顯示物件編號和標籤 -->
              <div class="card-header">
                <span class="post-id">{{ item.post_id }}</span>
                <span class="price">{{ item.tag }}</span>
              </div>
              <!-- 物件地址信息 -->
              <div class="address">{{ item.summary }}</div>
            </div>
          </div>
        </div>

        <!-- 加值服務卡片列表：當有數據時顯示 -->
        <div v-else-if="additionalList.length" class="cards-grid">
          <!-- 加值服務卡片項目：使用簡化版卡片樣式 -->
          <div
            v-for="item in additionalList"
            :key="item.title"
            class="property-card simple-card"
            @click="handleAdditionalClick(item.title)"
          >
            <!-- 加值服務信息容器 -->
            <div class="card-content">
              <!-- 套餐標題 -->
              <div class="package-title">{{ item.title }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../stores/user'
import { Loading } from '@element-plus/icons-vue'
import { getDialogueDesc } from "../utils/getDialogueDesc"
import request from '../api/request'

/**
 * 类型定义
 */
interface PostItem {
    post_id: string
    title: string
    thumbnail: string
    url: string
    summary: string
    tag: string
    hitMessage: string
}

interface AdditionalItem {
    title: string
}

interface ApiResponse {
    code: string
    message: string
    data?: {
        list?: PostItem[] | AdditionalItem[]
    }
}

/**
 * API 调用封装
 */
const api = {
    // 获取物件列表
    async getWareList(params: string) {
        return request<ApiResponse>({
            url: 'api/cancel/extend/warelist',
            data: {
                params,
                back_type: 'sale_addition'
            }
        })
    },

    // 获取加值服务列表
    async getAdditionalList(params: string, postId: string) {
        return request<ApiResponse>({
            url: 'api/cancel/extend/additionallist',
            data: {
                params,
                post_id: postId
            }
        })
    },

    // 取消加值服务
    async doBackAdditional(params: string, backPlanName: string) {
        return request<ApiResponse>({
            url: 'api/cancel/extend/doback',
            data: {
                params,
                back_plan_name: backPlanName
            }
        })
    }
}

/**
 * 响应式状态定义
 */
const message = ref('')
const loading = ref(false)
const postList = ref<PostItem[]>([])
const additionalList = ref<AdditionalItem[]>([])
const showAdditionalList = ref(false)
const userStore = useUserStore()
const showBackButton = ref(false)

/**
 * 获取物件列表
 */
const fetchPostList = async (isRetry = false) => {
    if (!isRetry) {
        loading.value = true
        message.value = ''
    }

    try {
        const params = getDialogueDesc(userStore.originalData)
        const response = await api.getWareList(params)

        // 处理临时错误（需要重试）
        if (response.code === '000002') {
            message.value = response.message
            postList.value = []
            setTimeout(() => fetchPostList(true), 3000)
            return
        }

        // 处理永久错误
        if (response.code === '000001') {
            message.value = response.message
            postList.value = []
            return
        }

        // 处理成功响应
        if (response.data?.list?.length) {
            postList.value = response.data.list as PostItem[]
            message.value = ''
        } else {
            message.value = '暫無可取消加值的物件'
            postList.value = []
        }
    } catch (error) {
        message.value = '請求失敗，請稍後重試'
        postList.value = []
    } finally {
        if (!isRetry) {
            loading.value = false
        }
    }
}

/**
 * 处理物件卡片点击
 */
const handlePostClick = async (postId: string) => {
    loading.value = true
    message.value = ''
    showAdditionalList.value = false

    try {
        const params = getDialogueDesc(userStore.originalData)
        const response = await api.getAdditionalList(params, postId)

        // 处理临时错误（需要重试）
        if (response.code === '000002') {
            message.value = response.message
            setTimeout(() => handlePostClick(postId), 3000)
            return
        }

        // 处理永久错误
        if (response.code === '000001') {
            message.value = response.message
            additionalList.value = []
            return
        }

        // 处理成功响应
        if (response.data?.list?.length) {
            additionalList.value = response.data.list as AdditionalItem[]
            showAdditionalList.value = true
            message.value = ''
        } else {
            message.value = '暫無可取消的加值服務'
            additionalList.value = []
        }
    } catch (error) {
        message.value = '請求失敗，請稍後重試'
        additionalList.value = []
    } finally {
        loading.value = false
    }
}

/**
 * 处理加值服务卡片点击
 */
const handleAdditionalClick = async (title: string) => {
    loading.value = true
    message.value = ''

    try {
        const params = getDialogueDesc(userStore.originalData)
        const response = await api.doBackAdditional(params, title)

        // 处理临时错误（需要重试）
        if (response.code === '000002') {
            message.value = response.message
            setTimeout(() => handleAdditionalClick(title), 3000)
            return
        }

        // 处理永久错误
        if (response.code === '000001') {
            message.value = response.message
            return
        }

        message.value = response.message || '操作成功'
        additionalList.value = []
        showAdditionalList.value = false
        showBackButton.value = true
    } catch (error) {
        message.value = '請求失敗，請稍後重試'
        showBackButton.value = true
    } finally {
        loading.value = false
    }
}

// 组件挂载时执行
onMounted(() =>
    {
      if (userStore.originalData) {
        getDialogueDesc(userStore.originalData);
      }
      fetchPostList()
    }
)
</script>

<style scoped>
/* 頁面整體佈局
-------------------------------------------------- */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;  /* 頁面居中 */
}

/* 消息提示框樣式
-------------------------------------------------- */
.message-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: auto;
  background: #f5f7fa;
  border-radius: 4px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  text-align: left;
  padding: 20px;
  line-height: 1.5;
  gap: 8px;

}

/* 消息框內的 HTML 內容樣式 */
.message-box :deep(p) {
  margin: 0;
  padding: 0;
}

.message-box :deep(a) {
  color: #307ae8;
  text-decoration: none;
}

.message-box :deep(a:hover) {
  text-decoration: underline;
}

.message-box :deep(br) {
  content: "";
  display: block;
  margin: 8px 0;  /* 設置段落間距 */
}

/* 加載狀態樣式
-------------------------------------------------- */
.loading {
  color: #409eff;
}

/* 加載內容容器 */
.loading-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}

/* 加載圖標動畫 */
.loading-icon {
  font-size: 20px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0); }
  to { transform: rotate(360deg); }
}

/* 主要內容區域樣式
-------------------------------------------------- */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
  overflow-y: auto;
}

/* 標題和描述文本樣式
-------------------------------------------------- */
.title {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #303133;
  font-weight: 500;
}

.description {
  margin: 0 0 16px 0;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
}

/* 卡片列表容器樣式
-------------------------------------------------- */
.cards-container {
  margin-top: 24px;
  max-height: calc(100vh - 300px);  /* 動態計算最大高度 */
  overflow-y: auto;
}

/* 卡片網格佈局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);  /* 兩列佈局 */
  gap: 20px;
  padding: 4px;
  padding-right: 12px;
  transition: opacity 0.3s ease;
}

/* 物件卡片樣式
-------------------------------------------------- */
.property-card {
  display: flex;
  background: #fff;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  height: 80px;
  cursor: pointer;
}

/* 卡片懸停和點擊效果 */
.property-card:hover {
  transform: translateY(-2px);  /* 懸停時上浮 */
}

.property-card:active {
  transform: scale(0.98);  /* 點擊時縮小 */
}

/* 卡片圖片容器 */
.card-image {
  width: 79px;
  height: 79px;
  flex-shrink: 0;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;  /* 圖片填充方式 */
}

/* 卡片內容樣式 */
.card-content {
  flex: 1;
  padding: 11px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 卡片頭部樣式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

/* 物件編號和價格樣式 */
.post-id {
  font-weight: 700;
  color: #f56c6c;
  font-size: 14px;
}

.price {
  color: #303133;
  font-weight: 700;
  font-size: 14px;
}

/* 地址文本樣式 */
.address {
  color: #303133;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  font-weight: 700;
  -webkit-line-clamp: 2;  /* 限制文本顯示兩行 */
  -webkit-box-orient: vertical;
}

/* 簡化版卡片樣式（用於加值服務）
-------------------------------------------------- */
.simple-card {
  height: auto;
  min-height: 60px;
}

.simple-card .card-content {
  padding: 16px;
}

.package-title {
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
  font-weight: 500;
}

/* 自定義滾動條樣式
-------------------------------------------------- */
.cards-container::-webkit-scrollbar {
  width: 6px;
}

.cards-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cards-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

/* 響應式佈局
-------------------------------------------------- */
@media (max-width: 768px) {
  /* 移動端適配 */
  .page-container {
    padding: 16px;
  }

  .content {
    padding: 16px;
  }

  /* 移動端單列顯示 */
  .cards-grid {
    grid-template-columns: 1fr;
  }

  .property-card {
    margin-bottom: 12px;
  }
}
</style>