"""
套餐延期相关API路由模块
提供套餐延期功能，包含以下特性：
1. 套餐延期请求处理
2. 自动重试机制（最多3次）
3. 错误处理和日志记录
4. 特定错误消息的自定义处理
"""

from fastapi import APIRouter
from pydantic import BaseModel, Field
from typing import Optional
import aiohttp
import asyncio
import json
from logger import log_async_task
from get_user_id import get_user_id_from_params

router = APIRouter()


class ApiResponse(BaseModel):
    """标准API响应模型"""
    code: str  # 响应代码：000000成功，其他为失败
    message: str = ""  # 响应消息
    data: Optional[dict] = None  # 响应数据，可选


class DelayPackageRequest(BaseModel):
    """套餐延期请求模型"""
    params: str  # 用户认证参数

    class Config:
        frozen = True  # 设置为不可变对象，提高安全性


@router.post("/delaypackage")
async def handle_delay_package(request: DelayPackageRequest) -> ApiResponse:
    """
    处理套餐延期请求
    
    处理逻辑：
    1. 验证用户身份
    2. 发送延期请求
    3. 处理响应结果
    4. 记录操作日志
    
    Args:
        request: 包含用户认证参数的请求对象
    
    Returns:
        ApiResponse: 标准响应对象，包含延期结果或错误信息
    """
    user_id = get_user_id_from_params(request.params)

    # 错误消息模板
    ERROR_NO_PACKAGE = "\n\n經查詢你的賬號內無符合延期的套餐，套餐延期條件如下：\n1、近180天內，賬號中有已過期且未用完的套餐\n2、需延期的套餐延期次數小於2次"

    async def do_request(session, url, data, headers):
        """
        处理单个请求的重试逻辑
        
        Args:
            session: aiohttp会话对象
            url: 请求URL
            data: 请求数据
            headers: 请求头
        
        Returns:
            tuple: (响应数据, 状态码)
        """
        max_retries = 3  # 最大重试次数
        retry_count = 0  # 当前重试次数
        retry_delay = 1  # 重试间隔（秒）

        while retry_count < max_retries:
            try:
                async with session.post(
                        url,
                        data=data,
                        headers=headers,
                        timeout=30
                ) as response:

                    if response.status == 200:
                        response_text = await response.text()

                        result = json.loads(response_text)
                        error_msg = result.get('errorMsg', '')

                        # 检查是否需要重试
                        if "重複操作" in error_msg:
                            retry_count += 1
                            if retry_count < max_retries:
                                await asyncio.sleep(retry_delay)
                                continue

                            # 重试次数用完，返回重复操作错误
                            return {
                                "code": "408",
                                "message": "請勿重複操作",
                                "data": {
                                    "success": False,
                                    "sub-title": "請勿重複操作"
                                }
                            }

                        return result, response.status

                    retry_count += 1
                    if retry_count < max_retries:
                        await asyncio.sleep(retry_delay)
                        continue

            except asyncio.TimeoutError:
                # 处理请求超时
                retry_count += 1
                if retry_count < max_retries:
                    await asyncio.sleep(retry_delay)
                    continue

            except Exception as e:
                # 处理其他异常
                retry_count += 1
                if retry_count < max_retries:
                    await asyncio.sleep(retry_delay)
                    continue

        # 所有重试都失败
        return {
            "code": "000000",
            "message": "請勿頻繁整理頁面",
            "data": {
                "success": False,
                "sub-title": "請勿頻繁整理頁面"
            }
        }, 500

    try:
        # 准备请求数据
        data = {
            'params': f'{request.params}'
        }

        api_url = "https://api.591.com.tw/api/sobot/package/doDefer"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with aiohttp.ClientSession() as session:
            result, status = await do_request(session, api_url, data, headers)

            # 处理响应结果
            error_msg = result.get('errorMsg', '')

            # 记录日志
            log_result = "成功" if "好的" in error_msg else "失败"
            log_desc = f"套餐延期{log_result}: {error_msg}"
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page="延期套餐",
                action_result=log_result,
                action_desc=log_desc
            ))

            # 处理不同情况的响应
            if "未登入" in error_msg or "登入狀態已失效" in error_msg:
                # 处理未登录或登录失效情况
                return ApiResponse(
                    code="000000",
                    message=error_msg,
                    data={
                        "success": False,
                        "sub-title": error_msg
                    }
                )

            if "好的" in error_msg:
                # 处理成功情况
                return ApiResponse(
                    code="000000",
                    message=error_msg,
                    data={
                        "success": True,
                        "sub-title": error_msg
                    }
                )

            if "經查詢" in error_msg:
                # 处理无可延期套餐情况
                return ApiResponse(
                    code="000000",
                    message=ERROR_NO_PACKAGE,
                    data={
                        "success": False,
                        "sub-title": ERROR_NO_PACKAGE
                    }
                )

            # 处理其他情况
            return ApiResponse(
                code="000000",
                message=error_msg or "請重新整理頁面後重試",
                data={
                    "success": False,
                    "sub-title": error_msg or "請重新整理頁面後重試"
                }
            )

    except Exception as e:
        # 记录错误日志
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="延期套餐",
            action_result="失败",
            action_desc=f"系统错误：{str(e)}"
        ))

        # 返回通用错误响应
        return ApiResponse(
            code="000000",
            message="請重新整理頁面後重試",
            data={
                "success": False,
                "sub-title": "請重新整理頁面後重試"
            }
        )