"""
Line QR码服务模块
提供Line群组查询和QR码展示功能
"""

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy import create_engine, Column, String, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 获取项目根目录
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 数据库配置
DB_CONFIG = {
    "host": os.getenv("DB_HOST"),
    "user": os.getenv("DB_USER"),
    "password": os.getenv("DB_PASSWORD"),
    "db": os.getenv("DB_NAME"),
    "port": int(os.getenv("DB_PORT")),
    "pool_size": int(os.getenv("DB_POOL_SIZE")),
    "pool_recycle": int(os.getenv("DB_POOL_RECYCLE"))
}

# 构建数据库URL
SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
    f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['db']}"
)

# 创建数据库引擎
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=DB_CONFIG['pool_size'],
    pool_recycle=DB_CONFIG['pool_recycle']
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


class User(Base):
    """用户数据模型"""
    __tablename__ = "line_users"
    user_id = Column(String(50), primary_key=True, index=True, comment="會員ID", nullable=False)
    line_group = Column(String(10), nullable=False, comment="Line群組", server_default=text("'3'"))


# 创建数据库表
Base.metadata.create_all(bind=engine)


def get_db():
    """数据库会话依赖"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 创建路由
router = APIRouter()


@router.get("/qrcode")
async def get_qrcode():
    """返回QR码页面"""
    return FileResponse(os.path.join(ROOT_DIR, "Line", "qrcode.html"))


@router.get("/line-group/{partner_id}")
async def get_line_group(partner_id: str, db: Session = Depends(get_db)):
    """获取用户的Line群组"""
    try:
        user = db.query(User).filter(User.user_id == partner_id).first()
        if not user:
            print(f"用户不存在: {partner_id}")
            return JSONResponse(content={"group": "3"})

        print(f"成功获取用户群组: {partner_id} -> {user.line_group}")
        return JSONResponse(content={"group": user.line_group})

    except Exception as e:
        print(f"获取Line群组失败: {str(e)}")
        return JSONResponse(content={"group": "3"})


# 导出路由器
line_database_router = router