<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐延期</title>
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500&display=swap" rel="stylesheet">
    <style>
        /* 基礎樣式設定 */
        body {
            font-family: 'Roboto', 'Microsoft JhengHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        /* 主容器樣式 */
        .container {
            max-width: 600px;
            width: 90%;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            position: relative;
            animation: fadeIn 0.5s ease-in-out;
        }

        /* 淡入動畫效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 載入遮罩層樣式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        /* 載入動畫樣式 */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        /* 載入文字樣式 */
        .loading-text {
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        /* 結果框樣式 */
        .result-box {
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
            animation: fadeIn 0.3s ease-in;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        /* 成功狀態樣式 */
        .success {
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
            color: #2e7d32;
        }

        /* 錯誤狀態樣式 */
        .error {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
        }

        /* 結果標題樣式 */
        .result-title {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 500;
            text-align: center;
            color: inherit;
        }

        /* 結果訊息樣式 */
        .result-message {
            margin: 0;
            font-size: 15px;
            line-height: 1.6;
            white-space: pre-line;
            text-align: left;
            padding: 0 15px;
            color: inherit;
        }

        /* 連結樣式 */
        .result-message a {
            color: #007bff;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        /* 連結懸停效果 */
        .result-message a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        /* 隱藏元素樣式 */
        .hidden {
            display: none;
        }

        /* 旋轉動畫 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 響應式設計 */
        @media (max-width: 480px) {
            /* 容器樣式調整 */
            .container {
                width: 95%;
                padding: 20px;
                margin: 15px;
            }

            /* 結果框樣式調整 */
            .result-box {
                padding: 15px;
                margin-top: 8px;
            }

            /* 標題樣式調整 */
            .result-title {
                font-size: 16px;
                margin-bottom: 6px;
            }

            /* 訊息內容樣式調整 */
            .result-message {
                font-size: 14px;
                padding: 0 10px;
            }

            /* 載入動畫樣式調整 */
            .loading-spinner {
                width: 35px;
                height: 35px;
            }

            /* 載入文字樣式調整 */
            .loading-text {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 載入動畫區域 -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">處理中，請稍候...</div>
        </div>

        <!-- 結果顯示區域 -->
        <div id="resultContainer" class="hidden">
            <div id="resultBox" class="result-box">
                <h3 id="resultTitle" class="result-title"></h3>
                <div id="resultMessage" class="result-message"></div>
            </div>
        </div>
    </div>

    <script>
        // 從 URL 獲取參數並解析
        function getParamsFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const params = urlParams.get('params');
            if (params) {
                try {
                    const decodedParams = decodeURIComponent(params);
                    try {
                        return JSON.parse(decodedParams);
                    } catch {
                        return decodedParams;
                    }
                } catch (error) {
                    console.error('參數解析錯誤:', error);
                    return null;
                }
            }
            return null;
        }

        // 顯示/隱藏載入動畫
        function toggleLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        // 顯示結果
        function showResult(result) {
            const resultContainer = document.getElementById('resultContainer');
            const resultBox = document.getElementById('resultBox');
            const resultTitle = document.getElementById('resultTitle');
            const resultMessage = document.getElementById('resultMessage');

            resultContainer.classList.remove('hidden');
            resultBox.className = 'result-box ' + (result.data.success ? 'success' : 'error');
            resultTitle.textContent = result.data.success ? '套餐延期成功' : '套餐延期失敗';

            // 使用 innerHTML 來支援 HTML 內容
            resultMessage.innerHTML = result.message;

            // 添加淡入動畫效果
            resultBox.style.opacity = '0';
            setTimeout(() => {
                resultBox.style.opacity = '1';
            }, 10);
        }

        // 調用 API
        async function callDelayPackageApi() {
            const params = getParamsFromURL();

            if (!params) {
                showResult({
                    code: '400',
                    message: '無效的請求參數',
                    data: {
                        success: false,
                        'sub-title': '無效的請求參數'
                    }
                });
                return;
            }

            toggleLoading(true);

            try {
                const response = await fetch('https://cs-ai.591.com.tw/api/delaypackage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        params: typeof params === 'string' ? params : JSON.stringify(params)
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP 錯誤！狀態碼：${response.status}`);
                }

                const result = await response.json();
                showResult(result);

            } catch (error) {
                console.error('API 調用錯誤:', error);
                showResult({
                    code: '500',
                    message: '系統錯誤，請稍後重試',
                    data: {
                        success: false,
                        'sub-title': '系統錯誤，請稍後重試'
                    }
                });
            } finally {
                toggleLoading(false);
            }
        }

        // 頁面載入完成後自動調用 API
        document.addEventListener('DOMContentLoaded', callDelayPackageApi);
    </script>
</body>
</html>