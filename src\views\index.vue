<template>
  <div class="page-container">
    <div class="content">
      <img src="/favicon.png" alt="Logo" class="logo">

      <div class="message-box">
        <p class="service-text" v-html="serverMessage"></p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import request from '../api/request.ts'
import { useUserStore } from '../stores/user'

const userStore = useUserStore()
const serverMessage = ref('')

const checkLoginStatus = async () => {
  try {
    const response = await request({
      url: 'api/user/info',
      params: { data: userStore.originalData }
    })

    serverMessage.value = (response as any).message

  } catch (error: any) {
    // 如果有响应数据，使用响应中的 message
    if (error.response && error.response.data) {
      serverMessage.value = error.response.data.message
    } else {
      // 如果没有响应数据，显示默认错误信息
      serverMessage.value = '系统错误，请稍后重试'
    }
  }
}

onMounted(() => {
  checkLoginStatus()
})
</script>

<style scoped>
/* 页面整体布局 */
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 主要内容区域 */
.content {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 700px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Logo 样式 */
.logo {
  width: 120px;
  height: auto;
  margin-bottom: 24px;
}

/* 消息框样式 */
.message-box {
  width: 100%;
  background: #ffffff;
  border-radius: 4px;
  padding: 20px;
  margin-top: 16px;
}

/* 服务消息文本样式 */
.service-text {
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  margin: 0;
  white-space: pre-line;
}

/* 链接样式 */
.service-text :deep(a) {
  color: #409EFF;
  text-decoration: none;
}

.service-text :deep(a:hover) {
  text-decoration: underline;
}

/* 段落间距 */
.service-text :deep(p) {
  margin: 0 0 12px 0;
}

/* 最后一个段落不需要底部间距 */
.service-text :deep(p:last-child) {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }

  .content {
    padding: 16px;
    width: 100%;
  }
}
</style>