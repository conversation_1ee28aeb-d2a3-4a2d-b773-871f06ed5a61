"""
取消物件相关API路由模块
提供获取可取消物件列表和执行取消物件操作的功能
主要处理两个端点：
1. /cancel/warelist - 获取可取消的物件列表
2. /cancel/doback - 执行物件取消操作
"""

from fastapi import APIRouter
from pydantic import BaseModel
from typing import List, Optional
import aiohttp
import asyncio
import json
from logger import log_async_task
from get_user_id import get_user_id_from_params

router = APIRouter()


# 基础响应模型
class ApiResponse(BaseModel):
    """标准API响应模型"""
    code: str  # 响应代码：000000成功，000001失败
    message: str = ""  # 响应消息
    data: Optional[dict] = None  # 响应数据，可选


# 基础请求模型
class BaseRequest(BaseModel):
    """基础请求模型，所有请求都需要包含用户认证参数"""
    params: str  # 用户认证参数

    class Config:
        frozen = True  # 设置为不可变对象，提高安全性


# 物件列表请求模型
class WareListRequest(BaseRequest):
    """获取物件列表的请求模型，继承基础请求模型"""
    pass


# 取消物件请求模型
class CancelRequest(BaseRequest):
    """取消物件的请求模型，继承基础请求模型"""
    post_id: str  # 要取消的物件ID


@router.post("/cancel/warelist")
async def handle_ware_list(request: WareListRequest) -> ApiResponse:
    """
    获取可取消的物件列表
    
    处理逻辑：
    1. 并发请求两个API获取物件列表
    2. 合并两个API的结果
    3. 处理错误情况和特殊响应
    
    Args:
        request: 包含用户认证参数的请求对象
    
    Returns:
        ApiResponse: 标准响应对象，包含物件列表或错误信息
    """
    user_id = get_user_id_from_params(request.params)

    async def fetch_api(session, url, data, api_num):
        """
        单个API的请求函数，包含重试逻辑
        
        Args:
            session: aiohttp会话对象
            url: 请求URL
            data: 请求数据
            api_num: API编号，用于日志标识
        
        Returns:
            dict: API响应数据
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                async with session.post(url, data=data, headers=headers, timeout=35) as response:
                    if response.status == 200:
                        response_text = await response.text()
                        result = json.loads(response_text)
                        print(f"\nAPI{api_num} Response (Attempt {retry_count + 1}):",
                              json.dumps(result, indent=2, ensure_ascii=False))

                        # 检查是否需要重试
                        if "請勿重複操作" in result.get("errorMsg", ""):
                            print(f"API{api_num} 需要重试 (Attempt {retry_count + 1})")
                            retry_count += 1
                            await asyncio.sleep(1)  # 等待1秒后重试
                            continue

                        return result

            except asyncio.TimeoutError:
                print(f"API{api_num} 超时 (Attempt {retry_count + 1})")
                retry_count += 1
                await asyncio.sleep(1)  # 等待1秒后重试
                continue

            except Exception as e:
                print(f"API{api_num} 错误 (Attempt {retry_count + 1}): {str(e)}")
                return {"code": "000001", "message": f"api{api_num}_error"}

        print(f"API{api_num} 重试次数已达上限")
        return {"code": "000001", "message": f"api{api_num}_error"}

    try:
        print(request.params)
        # 准备两个API的请求数据
        data1 = {
            'params': request.params,
            "back_type": "sale_ware"
        }
        data2 = {
            'params': request.params
        }

        # API端点
        api_url1 = "https://api.591.com.tw/api/sobot/backMoney/wareList"
        api_url2 = "https://api.591.com.tw/api/sobot/index?strategy=BackMoney&step_name=showWareList&back_type=1"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        async with aiohttp.ClientSession() as session:
            # 并发请求两个API，每个API都有自己的重试逻辑
            results = await asyncio.gather(
                fetch_api(session, api_url1, data1, 1),
                fetch_api(session, api_url2, data2, 2)
            )

            # 合并物件列表
            combined_list = []
            api_errors = {
                "api1_error": results[0].get("message") == "api1_error",
                "api2_error": results[1].get("message") == "api2_error"
            }

            # 处理成功响应，合并物件列表
            for result in results:
                if result.get("code") == "000000" and result.get("list"):
                    combined_list.extend(result["list"])

            # 检查是否两个API都成功
            if not api_errors["api1_error"] and not api_errors["api2_error"] and combined_list:
                asyncio.create_task(log_async_task(
                    user_id=user_id,
                    action_page="取消物件-获取物件列表",
                    action_result="成功",
                    action_desc="有符合退点条件的物件"
                ))
                return ApiResponse(
                    code="000000",
                    data={
                        "list": combined_list,
                        **api_errors
                    }
                )

            # 处理特殊错误情况
            for result in results:
                if result.get("code") == "000001":
                    if "未登入" in result.get("errorMsg", ""):
                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page="取消物件-获取物件列表",
                            action_result="失败",
                            action_desc="未登入会员"
                        ))
                        return ApiResponse(code="000001", message=result["errorMsg"])

            # 无可退点物件或部分API失败
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page="取消物件-获取物件列表",
                action_result="失败",
                action_desc="无符合退点条件的物件或API请求失败"
            ))
            return ApiResponse(
                code="000001",
                message="暫未查詢到可退點的物件，建議您使用<a href='/modifypost' target='_self' style='color: #307ae8;'><修改物件></a>進行更換",
                data=api_errors
            )

    except Exception as e:
        # 记录系统错误
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="取消物件-获取物件列表",
            action_result="失败",
            action_desc=f"系统错误：{str(e)}"
        ))
        return ApiResponse(code="000001", message="請求失敗，請稍後重試")


@router.post("/cancel/doback")
async def handle_cancel(request: CancelRequest) -> ApiResponse:
    """
    执行取消物件操作
    
    处理逻辑：
    1. 根据物件ID选择对应的API
    2. 发送取消请求
    3. 处理响应结果和错误情况
    
    Args:
        request: 包含用户参数和物件ID的请求对象
    
    Returns:
        ApiResponse: 标准响应对象，包含操作结果
    """
    print("\n=== 开始处理取消请求 ===")
    print("Request post_id:", request.post_id)
    print("Request params:", request.params)

    user_id = get_user_id_from_params(request.params)

    async def do_request(session, url, data, headers):
        """
        处理单个请求的重试逻辑
        
        Args:
            session: aiohttp会话对象
            url: 请求URL
            data: 请求数据
            headers: 请求头
        
        Returns:
            tuple: (响应数据, 状态码)
        """
        max_retries = 3
        retry_count = 0
        retry_delay = 1.5  # 重试间隔秒数

        while retry_count < max_retries:
            try:
                print(f"\n=== 尝试请求 (第{retry_count + 1}次) ===")
                async with session.post(
                        url,
                        data=data,
                        headers=headers,
                        timeout=200
                ) as response:
                    print(f"Response status: {response.status}")
                    print(f"Response headers: {response.headers}")

                    if response.status == 200:
                        response_text = await response.text()
                        print("\n=== API响应 ===")
                        print("Raw response:", response_text)

                        result = json.loads(response_text)
                        print("JSON response:", json.dumps(result, indent=2, ensure_ascii=False))

                        # 检查是否需要重试
                        if "請勿重複操作" in result.get("errorMsg", ""):
                            print(f"\n=== 需要重试 (第{retry_count + 1}次) ===")
                            retry_count += 1
                            if retry_count < max_retries:
                                print(f"等待 {retry_delay} 秒后重试...")
                                await asyncio.sleep(retry_delay)
                                continue

                        # 处理特定错误消息
                        if "取消失敗，請您於服務時間聯絡人工客服處理" in result.get("errorMsg", ""):
                            result[
                                "errorMsg"] = "該筆物件不符合取消條件，建議您使用<a href='/modifypost' target='_self' style='color: #307ae8;'><修改物件></a>進行更換"

                        return result, response.status

                    # 非200状态码，进行重试
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"等待 {retry_delay} 秒后重试...")
                        await asyncio.sleep(retry_delay)
                        continue

            except asyncio.TimeoutError:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    continue
                return {"code": "000001", "errorMsg": "請求超時"}, 408

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    continue
                return {"code": "000001", "errorMsg": "請求失敗"}, 500

        return {"code": "000001", "errorMsg": "重試次數已達上限"}, 500

    try:
        # 处理物件ID并准备请求数据
        post_id = request.post_id.upper()
        data = {
            'post_id': post_id,
            'params': f'{request.params}'
        }

        print("\n=== 准备发送的数据 ===")
        print("Post ID:", post_id)
        print("Request data:", json.dumps(data, indent=2, ensure_ascii=False))

        # 根据物件ID类型选择不同的API
        api_url = (
            "https://api.591.com.tw/api/sobot/index?strategy=BackMoney&step_name=doBack&back_type=1"
            if post_id.startswith('R') else
            "https://api.591.com.tw/api/sobot/backMoney/doBack?sence=1"
        )
        print("\n=== 选择的API ===")
        print("API URL:", api_url)

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        print("\n=== 开始发送请求 ===")
        async with aiohttp.ClientSession() as session:
            result, status = await do_request(session, api_url, data, headers)

            # 记录日志
            asyncio.create_task(log_async_task(
                user_id=user_id,
                action_page="取消物件-操作取消物件",
                action_result="成功" if status == 200 and result.get("code") == "000000" else "失败",
                action_desc=str(result)
            ))

            # 返回响应
            response_data = ApiResponse(
                code=result.get("code", "000001"),
                message=result.get("errorMsg", "請求失敗，請稍後重試")
            )
            print("\n=== 返回结果 ===")
            print("Response data:", response_data.dict())
            return response_data

    except Exception as e:
        print("\n=== 发生错误 ===")
        print("Error type:", type(e).__name__)
        print("Error message:", str(e))

        # 记录错误日志
        asyncio.create_task(log_async_task(
            user_id=user_id,
            action_page="取消物件-操作取消物件",
            action_result="失败",
            action_desc=f"系统错误：{str(e)}"
        ))
        return ApiResponse(code="000001", message="請求失敗，請稍後重試")