<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模擬 591 跳轉</title>
    <style>
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <button onclick="redirectToApp()">模擬從591首頁進入窗口</button>
    </div>

    <script>
        function redirectToApp() {
            const mockData = {
  "status": 1,
  "msg": "OK",
  "data": {
    "partnerid": "1435311",
    "realname": "客女士",
    "uname": "客女士",
    "face": "http://upload.591.com.tw/user/2022/12/14/167098018418641100.jpg",
    "user_label": "65fe7e366f43419389ce4408087647db",
    "is_vip": "0",
    "vip_level": "",
    "c_cf_dialogueDesc": "user_id%3D1435311%26timestamp%3D1737734470%26secretStr%3DzsbwmJlyoDHcDa42qOmMgMaqlX5lUBELisco4IYrCV20IlKWVK8D4xQ%252F%26secretStr2%3Dvnme%252FwVZar5ASoyQdS%252FG8aMPForwrm0SAnmQUecIj982pGqCQLXYzv1o%26device%3D4"
  }
};

            const data = JSON.stringify(mockData);

            // 计算窗口位置（居中）
            const width = 1020;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;

            window.open(
                `https://cs-ai.591.com.tw/?data=${data}`,
                '_blank',
                `width=${width},
                 height=${height},
                 left=${left},
                 top=${top},
                 resizable=no,
                 scrollbars=no,
                 status=no,
                 location=no,
                 menubar=no,
                 toolbar=no`
            );
        }
    </script>
</body>
</html>