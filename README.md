# 591自助服务系统

## 一、项目概述
591自助服务系统是一个基于 Vue 3 + TypeScript + FastAPI 构建的全栈 Web 应用，旨在为 591 网站用户提供丰富且实用的自助服务功能。

## 二、功能特性
- **用户认证与信息管理**：保障用户账户安全，方便用户对自身信息进行管理操作。
- **物件管理（修改、取消）**：用户能够对相关物件进行修改以及取消等操作，便于根据实际情况灵活调整。
- **套餐管理（延期、取消）**：针对不同的套餐，支持进行延期或者取消操作，满足多样化需求。
- **加值服务管理**：提供全面的加值服务管理功能，方便用户对加值服务进行相应操作。
- **完整的操作日志记录**：记录每一次操作详情，便于后续查看与追溯。

## 三、技术栈

### （一）前端
- **Vue 3**：用于构建用户界面，提供高效、灵活的开发体验。
- **TypeScript**：增强代码的可读性、可维护性以及类型安全性。
- **Element Plus**：一套基于 Vue 3 的桌面端组件库，助力快速搭建美观且易用的界面。
- **Pinia**：作为状态管理库，方便管理应用的状态逻辑。
- **Vue Router**：负责前端路由管理，实现页面之间的流畅切换。
- **Axios**：用于发起 HTTP 请求，与后端进行数据交互。

### （二）后端
- **FastAPI**：基于 Python 的快速 Web 框架，具备高性能、简洁的特点。
- **MySQL**：可靠的关系型数据库，用于存储系统数据。
- **aiomysql**：为异步操作 MySQL 数据库提供支持。
- **aiohttp**：在异步 HTTP 客户端/服务器端方面发挥作用。

## 四、系统要求
- **Node.js**：Node.js v22.12.0
- **Python**：Python 3.12
- **MySQL**：MySQL 8.0.36

## 五、快速开始

### （一）克隆项目
首先，将项目代码克隆到本地环境：
```bash
git clone <project-url>
```

### （二）安装依赖
1. 安装前端依赖：
```bash
pnpm install
```

2. 安装后端依赖：
```bash
pip install -r requirements.txt
```

### （三）配置数据库
1. 创建 MySQL 数据库
2. 修改 `API/logger.py` 中的数据库配置：
```python
DB_CONFIG = {
    "host": "your_host",
    "user": "your_username",
    "password": "your_password",
    "db": "your_database",
    "port": "your_port"
}
```

### （四）启动服务
1. 启动前端开发服务器：
```bash
pnpm dev
```

2. 启动后端服务器：
```bash
cd API
uvicorn app:app --reload --port 9993 --host 0.0.0.0
```

3.进入前端：

打开main.html文件，点击进入

## 六、项目结构
```
T5-AI/
├── API/                # 后端代码
│   ├── app.py         # 主应用入口
│   ├── logger.py      # 日志处理模块
│   ├── user.py        # 用户管理模块
│   ├── modify_post.py # 物件修改模块
│   └── ...           # 其他API模块
├── src/               # 前端源码
│   ├── components/    # 组件
│   ├── views/         # 页面
│   ├── router/        # 路由
│   ├── store/         # 状态管理
│   └── utils/         # 工具函数
├── public/            # 静态资源
└── package.json       # 项目配置
```

## 七、主要功能模块

### （一）用户管理
- 用户信息获取与展示
- 登录状态验证与维护

### （二）物件管理
- 获取可修改物件列表
- 物件信息修改功能
- 物件取消操作处理

### （三）套餐管理
- 套餐延期功能
- 售屋套餐取消
- 租屋套餐取消

### （四）加值服务
- 加值服务取消功能
- 服务列表获取与展示

### （五）日志系统
- 异步日志记录机制
- 批量处理优化
- 性能监控与统计

## 八、特色功能

- 🔄 **自动重试机制**：针对不稳定的网络环境，实现智能重试。
- 🎯 **精确的错误处理**：详细的错误分类与处理机制。
- 📊 **完整的日志记录**：系统操作全程可追踪。
- 🚀 **性能优化设计**：采用多项技术提升系统性能。
- 🌐 **中文界面支持**：完整的中文界面，操作更友好。

## 九、开发说明

### （一）API文档
启动后端服务后，访问 https://cs-ai.debug.591.com/docs 查看完整的API文档。

### （二）开发模式
- 前端开发服务器：https://cs-ai.debug.591.com
- 后端API服务器：https://cs-ai.debug.591.com

### （三）环境变量
前端请求后端的域名配置在 `src/api/request.ts` 文件中：
```env
  baseURL: 'https://cs-ai.debug.591.com' 
```

### （四）列出已安装的包
```env
  pip freeze > requirements.txt
```

---

