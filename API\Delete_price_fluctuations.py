import asyncio
from fastapi import APIRouter
from pydantic import BaseModel
import aiohttp
from typing import Dict, Any, Optional
from get_user_id import get_user_id_from_params

from logger import log_async_task

router = APIRouter()


# 请求模型
class WareListRequest(BaseModel):
    """获取物件列表的请求模型"""
    params: str  # 用户认证参数


class GetUrlRequest(BaseModel):
    """获取修改链接的请求模型"""
    post_id: str  # 物件ID
    params: str  # 用户认证参数


# API 请求工具函数
async def make_api_request(
        url: str,
        data: Dict[str, Any],
        user_id: str,
        action_page: str,
        max_retries: int = 2,
        retry_delay: int = 3
) -> Dict[str, Any]:
    retries = 0
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    while retries <= max_retries:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, headers=headers, timeout=200) as response:
                    if response.status != 200:
                        raise Exception("Network request failed")

                    api_data = await response.json()

                    # 处理重复操作
                    if (api_data.get("code") == "000001" and
                            api_data.get("errorMsg") == "請勿重複操作"):

                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="重试",
                            action_desc=f"第{retries + 1}次重试"
                        ))

                        if retries < max_retries:
                            retries += 1
                            await asyncio.sleep(retry_delay)
                            continue

                        return {
                            "code": "000001",
                            "message": "請勿重複操作",
                            "data": None
                        }

                    # 处理其他错误
                    if api_data.get("code") == "000001":
                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="失败",
                            action_desc=api_data.get("errorMsg", "未知错误")
                        ))
                        return {
                            "code": "000001",
                            "message": api_data.get("errorMsg"),
                            "data": None
                        }

                    # 处理成功情况
                    asyncio.create_task(log_async_task(
                        user_id=user_id,
                        action_page=action_page,
                        action_result="成功",
                        action_desc="请求成功"
                    ))
                    return {
                        "code": "000000",
                        "message": "成功",
                        "data": api_data
                    }

        except Exception as e:
            if retries == max_retries:
                asyncio.create_task(log_async_task(
                    user_id=user_id,
                    action_page=action_page,
                    action_result="失败",
                    action_desc=f"系统错误：{str(e)}"
                ))
                return {
                    "code": "000001",
                    "message": "請重新整理頁面後重試",
                    "data": None
                }
            retries += 1
            await asyncio.sleep(retry_delay)

# 路由处理函数
@router.post("/DeletePriceFluctuations/list")
async def handle_ware_list(request: WareListRequest):
    user_id = get_user_id_from_params(request.params)
    response = await make_api_request(
        url="https://api.591.com.tw/api/sobot/index?strategy=ClearPriceWave&step=1",
        data={'params': request.params},
        user_id=user_id,
        action_page="清空價格波動-获取物件列表"
    )

    if response["code"] == "000000":
        return {
            "code": "000000",
            "message": "成功",
            "data": {
                "list": response["data"].get("list", [])
            }
        }
    return response

@router.post("/DeletePriceFluctuations/Clear")
async def handle_get_url(request: GetUrlRequest):
    user_id = get_user_id_from_params(request.params)
    
    response = await make_api_request(
        url="https://api.591.com.tw/api/sobot/index?strategy=ClearPriceWave&step=3",
        data={
            'post_id': request.post_id,
            'params': request.params
        },
        user_id=user_id,
        action_page="清空價格波動-清除"
    )
    # 处理响应数据
    if response["code"] == "000001":
        return {
            "code": "000001",
            "message": f"好的，已為您處理物件{request.post_id}^^<br>您可至物件詳情頁確認看看",
            # "data": response["data"].get("data", {})
            "data": "好的，正在為您處理^^\n您現可至物件詳情頁確認是否有為您清空價格波動"
        }
    return response