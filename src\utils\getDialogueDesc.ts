/**
 * 從URL參數中提取並處理dialogueDesc
 * @param encodedData 編碼後的原始數據
 * @returns 完整的dialogueDesc JSON字符串
 *
 * URL編碼說明:
 * 1. URL中的特殊字符需要編碼，例如：
 *    = 編碼為 %3D
 *    & 編碼為 %26
 *    空格 編碼為 %20
 *    / 編碼為 %2F 或 %252F (雙重編碼)
 *    + 編碼為 %2B 或 %252B (雙重編碼)
 *
 * 2. 為什麼需要編碼：
 *    - URL只能使用ASCII字符
 *    - 特殊字符可能影響URL的解析
 *    - 保持數據的完整性和安全性
 *    - 避免跨站腳本攻擊(XSS)
 *
 * 3. 編碼/解碼過程：
 *    原始數據: user_id=123&time=456
 *    基本編碼: user_id%3D123%26time%3D456
 *    特殊字符雙重編碼: 包含/和+的字符會被編碼兩次
 */
export const getDialogueDesc = (encodedData: string): string => {

  // 驗證輸入數據是否為空
  if (!encodedData) {
    throw new Error('No data available')
  }

  // 確保編碼狀態的函數
  // 處理兩種情況：
  // 1. 基本字符編碼（=, &）
  // 2. 特殊字符雙重編碼（/, +）
  const ensureEncoded = (str: string) => {
    // 第一次編碼 - 處理基本字符
    let encoded = str.replace(/=/g, '%3D')  // 將等號轉換為%3D
                    .replace(/&/g, '%26')    // 將&符號轉換為%26
                    .replace(/\//g, '%2F')   // 將斜線轉換為%2F
                    .replace(/\+/g, '%2B')   // 將加號轉換為%2B

    // 第二次編碼 - 對特殊字符進行雙重編碼
    encoded = encoded.replace(/%2F/g, '%252F')  // 將%2F再次編碼為%252F
                    .replace(/%2B/g, '%252B')   // 將%2B再次編碼為%252B

    return encoded
  }

  // 1. 處理 data 參數格式
  // 例如：?data={"c_cf_dialogueDesc":"xxx"}
  const dataMatch = encodedData.match(/[?&]data=([^&]+)/)

  if (dataMatch) {
    // 提取data參數值
    const data = dataMatch[1]

    // 查找dialogueDesc的開始位置
    const startKey = 'c_cf_dialogueDesc%22:%22'
    const startIndex = data.indexOf(startKey)

    // 如果找不到dialogueDesc，拋出錯誤
    if (startIndex === -1) {
      console.log('Available keys:', data.match(/%22(.*?)%22/g))
      throw new Error('Cannot find dialogueDesc')
    }

    // 提取並編碼dialogueDesc的值
    const valueStart = startIndex + startKey.length
    const valueEnd = data.indexOf('%22', valueStart)
    const extractedValue = ensureEncoded(
      data.substring(valueStart, valueEnd)
    )

    // 構建最終的JSON字符串
    const result = `{"c_cf_dialogueDesc":"${extractedValue}"}`
    return result
  }

  // 2. 處理 params 參數格式
  // 例如：?params={"channel":"T591","c_cf_dialogueDesc":"xxx"}
  const paramsMatch = encodedData.match(/[?&]params=([^&]+)/)

  if (paramsMatch) {
    // 解碼並解析params參數
    const paramsStr = decodeURIComponent(paramsMatch[1])
    const params = JSON.parse(paramsStr)

    // 如果存在dialogueDesc，進行編碼處理
    if (params.c_cf_dialogueDesc) {
      const encodedValue = ensureEncoded(params.c_cf_dialogueDesc)
      return JSON.stringify({ c_cf_dialogueDesc: encodedValue })
    }
  }

  // 如果無法找到dialogueDesc，拋出錯誤
  throw new Error('Cannot find dialogueDesc')
}

/**
 * URL編碼/解碼過程示例:
 *
 * 1. 原始數據:
 * {
 *   "c_cf_dialogueDesc": "user_id=123&timestamp=456"
 * }
 *
 * 2. URL編碼後:
 * {
 *   "c_cf_dialogueDesc": "user_id%3D123%26timestamp%3D456"
 * }
 *
 * 3. 如果URL被瀏覽器自動解碼:
 * {
 *   "c_cf_dialogueDesc": "user_id=123&timestamp=456"
 * }
 *
 * 4. ensureEncoded函數重新編碼後:
 * {
 *   "c_cf_dialogueDesc": "user_id%3D123%26timestamp%3D456"
 * }
 *
 * 注意：包含特殊字符(/和+)時會進行雙重編碼
 */
