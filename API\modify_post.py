"""
物件修改相关API路由模块
提供物件修改相关功能，包含以下特性：
1. 获取可修改物件列表
2. 获取物件修改链接
3. 自动重试机制
4. 错误处理和日志记录
"""

import asyncio
from fastapi import APIRouter
from pydantic import BaseModel
import aiohttp
from typing import Dict, Any, Optional
from get_user_id import get_user_id_from_params

from logger import log_async_task

router = APIRouter()


# 请求模型
class WareListRequest(BaseModel):
    """获取物件列表的请求模型"""
    params: str  # 用户认证参数


class GetUrlRequest(BaseModel):
    """获取修改链接的请求模型"""
    post_id: str  # 物件ID
    params: str  # 用户认证参数


# API 请求工具函数
async def make_api_request(
        url: str,
        data: Dict[str, Any],
        user_id: str,
        action_page: str,
        max_retries: int = 2,
        retry_delay: int = 3
) -> Dict[str, Any]:
    """
    统一的 API 请求处理函数
    
    处理逻辑：
    1. 发送API请求
    2. 处理重复操作情况
    3. 处理错误情况
    4. 记录操作日志
    
    Args:
        url: 请求URL
        data: 请求数据
        user_id: 用户ID
        action_page: 操作页面
        max_retries: 最大重试次数
        retry_delay: 重试间隔(秒)
    
    Returns:
        Dict[str, Any]: 标准响应对象
    """
    retries = 0
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }

    while retries <= max_retries:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, headers=headers, timeout=200) as response:
                    if response.status != 200:
                        raise Exception("Network request failed")

                    api_data = await response.json()

                    # 处理重复操作
                    if (api_data.get("code") == "000001" and
                            api_data.get("errorMsg") == "請勿重複操作"):

                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="重试",
                            action_desc=f"第{retries + 1}次重试"
                        ))

                        if retries < max_retries:
                            retries += 1
                            await asyncio.sleep(retry_delay)
                            continue

                        return {
                            "code": "000001",
                            "message": "請勿重複操作",
                            "data": None
                        }

                    # 处理其他错误
                    if api_data.get("code") == "000001":
                        asyncio.create_task(log_async_task(
                            user_id=user_id,
                            action_page=action_page,
                            action_result="失败",
                            action_desc=api_data.get("errorMsg", "未知错误")
                        ))
                        return {
                            "code": "000001",
                            "message": api_data.get("errorMsg"),
                            "data": None
                        }

                    # 处理成功情况
                    asyncio.create_task(log_async_task(
                        user_id=user_id,
                        action_page=action_page,
                        action_result="成功",
                        action_desc="请求成功"
                    ))
                    return {
                        "code": "000000",
                        "message": "成功",
                        "data": api_data
                    }

        except Exception as e:
            if retries == max_retries:
                asyncio.create_task(log_async_task(
                    user_id=user_id,
                    action_page=action_page,
                    action_result="失败",
                    action_desc=f"系统错误：{str(e)}"
                ))
                return {
                    "code": "000001",
                    "message": "請重新整理頁面後重試",
                    "data": None
                }
            retries += 1
            await asyncio.sleep(retry_delay)


# 路由处理函数
@router.post("/modify/warelist")
async def handle_ware_list(request: WareListRequest):
    """
    获取物件列表
    
    处理逻辑：
    1. 从请求参数中获取用户ID
    2. 调用API获取物件列表
    3. 处理响应数据
    
    Args:
        request: 包含用户认证参数的请求对象
    
    Returns:
        Dict: 包含物件列表的响应对象
    """
    user_id = get_user_id_from_params(request.params)

    response = await make_api_request(
        url="https://api.591.com.tw/api/sobot/wareEdit/wareList",
        data={'params': request.params},
        user_id=user_id,
        action_page="修改物件-获取物件列表"
    )

    # 处理响应数据
    if response["code"] == "000000":
        return {
            "code": "000000",
            "message": "成功",
            "data": {
                "list": response["data"].get("list", [])
            }
        }
    return response


@router.post("/modify/geturl")
async def handle_get_url(request: GetUrlRequest):
    """
    获取修改链接
    
    处理逻辑：
    1. 从请求参数中获取用户ID
    2. 调用API获取修改链接
    3. 处理响应数据
    
    Args:
        request: 包含物件ID和用户认证参数的请求对象
    
    Returns:
        Dict: 包含修改链接的响应对象
    """
    user_id = get_user_id_from_params(request.params)

    response = await make_api_request(
        url="https://api.591.com.tw/api/sobot/wareEdit/getUrl",
        data={
            'post_id': request.post_id,
            'params': request.params
        },
        user_id=user_id,
        action_page="修改物件-获取修改链接"
    )

    # 处理响应数据
    if response["code"] == "000000":
        return {
            "code": "000000",
            "message": "成功",
            "data": response["data"].get("data", {})
        }
    return response