import { defineStore } from 'pinia'
import { getDialogueDesc } from '../utils/getDialogueDesc.ts'
import { BASE_URL } from '../config/api'

export const useUserStore = defineStore('user', {
  state: () => ({
    originalData: sessionStorage.getItem('originalData') || ''
  }),

  getters: {
    parsedUserData(): any {
      try {
        if (!this.originalData) return null
        const jsonStr = decodeURIComponent(this.originalData)
        const data = JSON.parse(jsonStr)
        return data.data
      } catch {
        return null
      }
    }
  },

  actions: {
    setOriginalData(data: string) {
      this.originalData = data
      sessionStorage.setItem('originalData', data)
    },

    clearData() {
      this.originalData = ''
      sessionStorage.removeItem('originalData')
    },

    async verifyUser() {
      try {
        if (!this.originalData) {
          throw new Error('No data available')
        }

        // 使用工具函数获取 dialogueDesc
        const body = getDialogueDesc(this.originalData)

        // 使用 BASE_URL 构建完整的 URL
        const response = await fetch(`${BASE_URL}/api/verify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: body
        })

        return await response.json()

      } catch (error) {
        console.error('api call failed:', error)
        throw error
      }
    }
  }
})
