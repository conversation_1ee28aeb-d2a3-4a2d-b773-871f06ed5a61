"""
异步日志处理模块
提供高性能的异步日志记录功能，包含以下特性：
1. 批量日志处理
2. 数据库连接池管理
3. 自动重试机制
4. 性能指标收集
5. 健康检查
"""

from fastapi import APIRouter
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Tuple, List, Dict
import aiomysql
import asyncio
from contextlib import asynccontextmanager
import time
from collections import deque
import os
from dotenv import load_dotenv
load_dotenv()

router = APIRouter()

# 数据库配置
DB_CONFIG = {
    "host": os.getenv("DB_HOST"),
    "user": os.getenv("DB_USER"),
    "password": os.getenv("DB_PASSWORD"),
    "db": os.getenv("DB_NAME"),
    "port": int(os.getenv("DB_PORT")),
    "pool_size": int(os.getenv("DB_POOL_SIZE")),
    "pool_recycle": int(os.getenv("DB_POOL_RECYCLE")),
    "connect_timeout": int(os.getenv("DB_CONNECT_TIMEOUT")),
    "max_retry": int(os.getenv("DB_MAX_RETRY"))
}

# 日志批处理配置
BATCH_CONFIG = {
    "batch_size": int(os.getenv("BATCH_SIZE")),
    "flush_interval": int(os.getenv("FLUSH_INTERVAL")),
    "queue_size": int(os.getenv("QUEUE_SIZE"))
}


class LogData(BaseModel):
    """日志数据模型"""
    user_id: str  # 用户ID
    action_page: str  # 操作页面
    action_result: str  # 操作结果
    action_desc: str  # 操作描述

class LogMetrics:
    """
    日志性能指标收集类
    用于监控和统计日志写入性能
    """

    def __init__(self):
        self.total_logs = 0  # 总日志数
        self.failed_logs = 0  # 失败日志数
        self.total_time = 0  # 总处理时间
        self.batch_writes = 0  # 批量写入次数
        self._reset_time = time.time()  # 重置时间点

    def record_write(self, success: bool, duration: float, batch_size: int = 1):
        """
        记录一次写入操作的性能指标
        
        Args:
            success: 写入是否成功
            duration: 写入耗时
            batch_size: 批量大小
        """
        self.total_logs += batch_size
        if not success:
            self.failed_logs += batch_size
        self.total_time += duration
        self.batch_writes += 1

    @property
    def avg_write_time(self):
        """计算平均写入时间"""
        return self.total_time / self.batch_writes if self.batch_writes > 0 else 0

    @property
    def error_rate(self):
        """计算错误率"""
        return self.failed_logs / self.total_logs if self.total_logs > 0 else 0

    def reset(self):
        """重置所有指标"""
        self.__init__()


class LogBatcher:
    """
    日志批处理器
    管理日志队列和批量写入操作
    """

    def __init__(self):
        self.queue = deque(maxlen=BATCH_CONFIG["queue_size"])  # 日志队列
        self.flush_task = None  # 定时刷新任务
        self.metrics = LogMetrics()  # 性能指标收集器

    async def add(self, log_data: Dict):
        """
        添加日志到队列
        
        Args:
            log_data: 日志数据字典
        
        Returns:
            bool: 是否添加成功
        """
        if len(self.queue) >= BATCH_CONFIG["queue_size"]:
            print("警告: 日志队列已满")
            return False

        self.queue.append(log_data)

        # 启动定时刷新任务
        if self.flush_task is None:
            self.flush_task = asyncio.create_task(self._auto_flush())

        # 队列达到批处理大小时立即刷新
        if len(self.queue) >= BATCH_CONFIG["batch_size"]:
            await self.flush()

        return True

    async def flush(self):
        """
        强制刷新队列中的日志到数据库
        包含错误处理和重试机制
        """
        if not self.queue:
            return

        logs = []
        try:
            while self.queue and len(logs) < BATCH_CONFIG["batch_size"]:
                logs.append(self.queue.popleft())

            if logs:
                start_time = time.time()
                success = await insert_logs_batch(logs)
                duration = time.time() - start_time
                self.metrics.record_write(success, duration, len(logs))

        except Exception as e:
            print(f"批量写入失败: {str(e)}")
            # 写入失败时将日志放回队列
            self.queue.extendleft(reversed(logs))

    async def _auto_flush(self):
        """定时自动刷新任务"""
        while True:
            await asyncio.sleep(BATCH_CONFIG["flush_interval"])
            await self.flush()


class DBPool:
    """
    数据库连接池管理类
    提供连接池的创建、获取、关闭等功能
    """
    _pool = None  # 连接池实例
    _health_check_task = None  # 健康检查任务

    @classmethod
    async def get_pool(cls):
        """获取或创建数据库连接池"""
        if cls._pool is None:
            cls._pool = await aiomysql.create_pool(
                minsize=5,  # 最小连接数
                maxsize=DB_CONFIG['pool_size'],
                pool_recycle=DB_CONFIG['pool_recycle'],
                connect_timeout=DB_CONFIG['connect_timeout'],
                autocommit=True,
                **{k: v for k, v in DB_CONFIG.items()
                   if k not in ['pool_size', 'pool_recycle', 'connect_timeout', 'max_retry']}
            )
            # 启动健康检查
            if cls._health_check_task is None:
                cls._health_check_task = asyncio.create_task(cls._health_check())
        return cls._pool

    @classmethod
    async def close_pool(cls):
        """关闭数据库连接池"""
        if cls._pool is not None:
            if cls._health_check_task:
                cls._health_check_task.cancel()
            cls._pool.close()
            await cls._pool.wait_closed()
            cls._pool = None

    @classmethod
    @asynccontextmanager
    async def get_conn(cls):
        """
        获取数据库连接的上下文管理器
        
        使用示例:
        async with DBPool.get_conn() as cursor:
            await cursor.execute(sql)
        """
        pool = await cls.get_pool()
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    yield cursor
        except Exception as e:
            print(f"获取数据库连接失败: {str(e)}")
            raise

    @classmethod
    async def _health_check(cls):
        """
        定期检查连接池健康状态
        每分钟执行一次简单查询以确保连接可用
        """
        while True:
            await asyncio.sleep(60)  # 每分钟检查一次
            if cls._pool:
                try:
                    async with cls.get_conn() as cursor:
                        await cursor.execute("SELECT 1")
                except Exception as e:
                    print(f"连接池健康检查失败: {str(e)}")


# 创建全局日志批处理器
log_batcher = LogBatcher()


async def insert_logs_batch(logs: List[Dict]) -> bool:
    """
    批量插入日志到数据库
    
    Args:
        logs: 日志数据列表
    
    Returns:
        bool: 是否插入成功
    """
    try:
        async with DBPool.get_conn() as cursor:
            sql = """
            INSERT INTO t5_index_user_action_logs 
            (user_id, action_time, action_page, action_result, action_desc) 
            VALUES (%s, %s, %s, %s, %s)
            """
            values = [
                (log['user_id'], datetime.now(), log['action_page'],
                 log['action_result'], log['action_desc'])
                for log in logs
            ]
            await cursor.executemany(sql, values)
            return True
    except Exception as e:
        print(f"批量写入日志失败: {str(e)}")
        return False


async def insert_log(
        user_id: str,
        action_page: str,
        action_result: str,
        action_desc: str
) -> Tuple[bool, Optional[int], Optional[str]]:
    """
    异步日志记录函数
    
    Args:
        user_id: 用户ID
        action_page: 操作页面
        action_result: 操作结果
        action_desc: 操作描述
    
    Returns:
        Tuple[bool, Optional[int], Optional[str]]: 
        - 是否成功
        - 日志ID（如果直接写入）
        - 错误信息（如果有）
    """
    try:
        # 添加到批处理队列
        success = await log_batcher.add({
            'user_id': user_id,
            'action_page': action_page,
            'action_result': action_result,
            'action_desc': action_desc
        })

        if not success:
            # 队列满时直接写入
            async with DBPool.get_conn() as cursor:
                sql = """
                INSERT INTO t5_index_user_action_logs 
                (user_id, action_time, action_page, action_result, action_desc) 
                VALUES (%s, %s, %s, %s, %s)
                """
                values = (user_id, datetime.now(), action_page, action_result, action_desc)
                await cursor.execute(sql, values)
                return True, cursor.lastrowid, None

        return True, None, None

    except Exception as e:
        error_msg = f"日志记录失败: {str(e)}"
        print(error_msg)
        return False, None, error_msg


class AsyncLogger:
    """
    异步日志记录器
    提供便捷的日志记录接口
    """
    def __init__(self):
        self.log_batcher = log_batcher  # 使用全局的 log_batcher

    async def log_async(self, user_id: str, action_page: str,
                        action_result: str, action_desc: str) -> None:
        """
        异步日志包装方法，处理任务创建和错误处理
        
        Args:
            user_id: 用户ID
            action_page: 操作页面
            action_result: 操作结果
            action_desc: 操作描述
        """
        try:
            log_task = asyncio.create_task(insert_log(
                user_id=user_id,
                action_page=action_page,
                action_result=action_result,
                action_desc=action_desc
            ))

            # 设置超时等待
            try:
                await asyncio.wait_for(log_task, timeout=1.0)
            except asyncio.TimeoutError:
                print(f"日志写入超时: {action_page}")
            except Exception as e:
                print(f"日志写入异常: {str(e)}")

        except Exception as e:
            print(f"创建日志任务失败: {str(e)}")


# 创建全局 logger 实例
async_logger = AsyncLogger()


async def log_async_task(*args, **kwargs):
    """
    便捷的日志记录函数，自动处理异步任务
    
    使用示例:
    await log_async_task(
        user_id="123",
        action_page="首页",
        action_result="成功",
        action_desc="用户访问首页"
    )
    """
    return await async_logger.log_async(*args, **kwargs)

@router.post("/log")
async def create_log(log_data: LogData):
    success, log_id, error = await insert_log(
        log_data.user_id,
        log_data.action_page,
        log_data.action_result,
        log_data.action_desc
    )

    if success:
        return {"status": "success", "log_id": log_id}
    else:
        return {"status": "error", "message": error}


@router.get("/metrics")
async def get_metrics():
    """获取日志性能指标"""
    return {
        "total_logs": log_batcher.metrics.total_logs,
        "failed_logs": log_batcher.metrics.failed_logs,
        "avg_write_time": log_batcher.metrics.avg_write_time,
        "error_rate": log_batcher.metrics.error_rate,
        "queue_size": len(log_batcher.queue)
    }


# 应用启动时初始化
@router.on_event("startup")
async def startup():
    await DBPool.get_pool()


# 应用关闭时清理
@router.on_event("shutdown")
async def shutdown():
    # 确保所有日志都写入
    await log_batcher.flush()
    await DBPool.close_pool()