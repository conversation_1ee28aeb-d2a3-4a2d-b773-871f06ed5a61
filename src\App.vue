<template>
  <!-- 主佈局容器：僅在初始化完成後顯示 -->
  <el-container class="layout-container">
    <!-- 左側導航欄：根據URL參數控制顯示 -->
    <el-aside width="200px" v-show="!hideAside">
      <!-- Element Plus 導航菜單組件 -->
      <el-menu
        :default-active="activeMenu"
        :default-openeds="['1']"
        class="el-menu-vertical-demo"
        @open="handleOpen"
        @close="handleClose"
        :router="true"
      >
        <!-- 取消退點子選單 -->
        <el-sub-menu index="1">
          <!-- 子選單標題 -->
          <template #title>
            <div class="menu-icon">
              <img :src="cancelIcon" alt="cancel" class="custom-icon">
            </div>
            <span>取消退點</span>
          </template>
          <!-- 子選單項目 -->
          <el-menu-item index="/CancelPost">取消物件</el-menu-item>
          <el-menu-item index="/CancelExtend">取消加值</el-menu-item>
          <el-menu-item index="/SaleCancelPackage">取消出售套餐</el-menu-item>
          <el-menu-item index="/RentCancelPackage">取消出租套餐</el-menu-item>
        </el-sub-menu>

        <!-- 修改物件選單項 -->
        <el-menu-item index="/ModifyPost">
          <div class="menu-icon">
            <img :src="modifyIcon" alt="modify" class="custom-icon">
          </div>
          <span>修改物件</span>
        </el-menu-item>

        <!-- 套餐延期選單項 -->
        <el-menu-item index="/DelayPackage">
          <div class="menu-icon">
            <img :src="delayIcon" alt="delay" class="custom-icon">
          </div>
          <span>套餐延期</span>
        </el-menu-item>

        <!-- 獲取7-11繳費代碼選單項 -->
        <el-menu-item index="/Get711PayNumber">
          <div class="menu-icon">
            <img :src="PayIcon" alt="Pay" class="custom-icon">
          </div>
          <span>7-11儲值</span>
        </el-menu-item>

        <!-- 獲取7-11繳費代碼選單項 -->
        <el-menu-item index="/DeletePriceFluctuations">
          <div class="menu-icon">
            <img :src="DeleteIcon" alt="Pay" class="custom-icon">
          </div>
          <span>清空價格波動</span>
        </el-menu-item>

      </el-menu>
    </el-aside>

    <!-- 主內容區域：路由視圖 -->
    <el-container class="main-container">
      <router-view></router-view>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
/**
 * 引入依賴
 */
import { useRoute } from 'vue-router'
import { useUserStore } from './stores/user'
import { onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 導入菜單圖標
 */
import cancelIcon from './assets/cancel.png'
import modifyIcon from './assets/modify.png'
import delayIcon from './assets/delay.png'
import PayIcon from './assets/Pay.png'
import DeleteIcon from './assets/Delete.png'

/**
 * 狀態初始化
 */
const route = useRoute()                    // 路由實例
const userStore = useUserStore()            // 用戶狀態管理

/**
 * 計算是否隱藏側邊欄
 */
const hideAside = computed(() => {
  // 檢查route和route.query是否存在
  if (!route || !route.query) {
    return false
  }
  // 從URL參數中獲取hideAside的值
  // 如果參數值為'true'或'1'，則隱藏側邊欄
  const param = route.query.hideAside
  return param === 'true' || param === '1'
})

/**
 * 計算當前激活的菜單項
 */
const activeMenu = computed(() => {
  return route.path
})

/**
 * 菜單事件處理
 */
const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
</script>

<style>
/* 全局基礎樣式
-------------------------------------------------- */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;  /* 防止頁面出現滾動條 */
}

#app {
  height: 100%;
  width: 100%;
}
</style>

<style scoped>
/* 主佈局容器
-------------------------------------------------- */
.layout-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 側邊欄樣式
-------------------------------------------------- */
.el-aside {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  height: 100%;
  overflow-y: auto;    /* 內容超出時顯示滾動條 */
  overflow-x: hidden;  /* 隱藏水平滾動條 */
}

/* 主內容區域
-------------------------------------------------- */
.main-container {
  height: 100%;
  overflow: auto;  /* 內容超出時顯示滾動條 */
}

/* 導航菜單樣式
-------------------------------------------------- */
/* 移除菜單右側邊框 */
.el-menu {
  border-right: none;
}

/* 一級菜單項內邊距 */
.el-menu > .el-menu-item {
  padding-left: 20px !important;
}

/* 子菜單標題內邊距 */
.el-sub-menu__title {
  padding-left: 20px !important;
}

/* 二級菜單項樣式 */
.el-sub-menu .el-menu-item {
  padding-left: 40px !important;
  min-width: 199px;
}

/* 菜單圖標樣式
-------------------------------------------------- */
/* 圖標容器 */
.menu-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 12px;
}

/* 自定義圖標 */
.custom-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;  /* 保持圖片比例 */
}

/* 菜單文字樣式
-------------------------------------------------- */
.el-menu-item span,
.el-sub-menu__title span {
  font-size: 14px;
  color: #303133;
}

/* 菜單項狀態樣式
-------------------------------------------------- */
/* 激活狀態 */
.el-menu-item.is-active {
  background-color: #ecf5ff;
  color: #409EFF;
}

/* 懸浮狀態 */
.el-menu-item:hover {
  background-color: #f5f7fa;
}

/* 內容區域背景
-------------------------------------------------- */
.el-container {
  background-color: #f0f2f5;
}
</style>