<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加入專屬客服Line</title>
    <style>
        /* 頁面整體布局 */
        body {
            display: flex;
            justify-content: center;    /* 水平居中 */
            align-items: center;        /* 垂直居中 */
            height: 100vh;              /* 視窗高度 */
            margin: 0;
            flex-direction: column;     /* 垂直排列 */
            background-color: #ffffff;  /* 白色背景 */
        }
        /* 段落文字置中 */
        p {
            text-align: center;
        }
        /* QR碼圖片樣式 */
        #qrCode {
            width: 80vw;               /* 寬度為視窗寬度的80% */
            height: 80vw;              /* 高度等於寬度，保持正方形 */
            object-fit: contain;       /* 保持圖片比例 */
            max-width: 350px;          /* 最大寬度 */
            max-height: 350px;         /* 最大高度 */
        }
        /* 響應式設計：螢幕寬度大於768px時的樣式 */
        @media screen and (min-width: 768px) {
            #qrCode {
                width: 350px;          /* 固定寬度 */
                height: 350px;         /* 固定高度 */
            }
        }
    </style>
</head>
<body>
    <p><img id="qrCode" src="" alt="Line QR Code"></p>
    <p><span style="color: rgb(0, 0, 0); font-size: 22px;"><strong id="mainText">掃描行動條碼即可將官方帳號加入好友</strong></span></p>
    <p>請先點選LINE應用程式搜尋欄位旁的掃描圖示，再掃描此行動條碼。</p>

    <script>
        // 獲取當前網域
        const baseUrl = window.location.origin;

        // 從URL獲取參數
        const urlParams = new URLSearchParams(window.location.search);
        const partnerId = urlParams.get('partnerId');
        const group = urlParams.get('group');

        // 設置預設QR碼和文案
        let qrCodePath = '3';
        let managerText = '掃描行動條碼即可添加您的專屬客戶經理-Summer';

        // 根據group參數獲取Line QRcode
        if (group) {
            if (group === '1') {
                qrCodePath = '1';
                managerText = '掃描行動條碼即可添加您的專屬客戶經理-Ruby';
            } else if (group === '3') {
                qrCodePath = '3';
                managerText = '掃描行動條碼即可添加您的專屬客戶經理-Summer';
            }else if (group === '4') {
                qrCodePath = '4';
                managerText = '掃描行動條碼即可添加您的專屬客戶經理-Nina';
            }else if (group === '5') {
                qrCodePath = '5';
                managerText = '掃描行動條碼即可添加您的專屬客戶經理-Chloe';
            }
            document.getElementById('qrCode').src = `${baseUrl}/Line/image/${qrCodePath}.jpg`;
            document.getElementById('mainText').textContent = managerText;
        }
        // 如果沒有group參數但有partnerId參數，則使用原有的API邏輯
        else if (partnerId) {
            fetch(`${baseUrl}/api/line-group/${partnerId}`)
                .then(response => response.json())
                .then(data => {
                    const group = data.group;
                    document.getElementById('qrCode').src = `${baseUrl}/Line/image/${group}.jpg`;

                    if (group === '1') {
                        document.getElementById('mainText').textContent =
                            '掃描行動條碼即可添加您的專屬客戶經理-Ruby';
                    } else if (group === '3') {
                        document.getElementById('mainText').textContent =
                            '掃描行動條碼即可添加您的專屬客戶經理-Summer';
                    }else if (group === '4') {
                        document.getElementById('mainText').textContent =
                            '掃描行動條碼即可添加您的專屬客戶經理-Nina';
                    }else if (group === '5') {
                        document.getElementById('mainText').textContent =
                            '掃描行動條碼即可添加您的專屬客戶經理-Chloe';
                    }
                })
                .catch(error => {
                    console.error('獲取群組失敗:', error);
                    document.getElementById('qrCode').src = `${baseUrl}/Line/image/3.jpg`;
                    document.getElementById('mainText').textContent =
                        '掃描行動條碼即可添加您的專屬客戶經理-Summer';
                });
        }
        // 如果既沒有group也沒有partnerId參數，則顯示3組Line QRcode
        else {
            document.getElementById('qrCode').src = `${baseUrl}/Line/image/3.jpg`;
            document.getElementById('mainText').textContent = managerText;
        }
    </script>
</body>
</html>