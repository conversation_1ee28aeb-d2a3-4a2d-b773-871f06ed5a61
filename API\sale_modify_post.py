from fastapi import FastAP<PERSON>, APIRouter, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, String, DateTime, Integer
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import os
from dotenv import load_dotenv
import requests
import re
import logging
from urllib.parse import urlparse, parse_qs, unquote
import json

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('modify_post.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 加載環境變量
load_dotenv()

# 數據庫配置
DATABASE_URL = (
    f"mysql+aiomysql://{os.getenv('DB_USER')}:{os.getenv('DB_PASSWORD')}@"
    f"{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
    f"?charset=utf8mb4"
)

# 創建異步引擎
engine = create_async_engine(
    DATABASE_URL,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=1800
)

# 創建異步會話
async_session = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()

class PopLog(Base):
    __tablename__ = "poplog"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(String(50), nullable=False)
    time = Column(DateTime, nullable=False)
    post_id = Column(String(50), nullable=False)
    type = Column(String(20), nullable=False)
    response_result = Column(String(500), nullable=False)
    result = Column(String(50), nullable=False)

class URLRequest(BaseModel):
    url: str
    post_id: str

async def get_db():
    async with async_session() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

async def insert_to_database(db: AsyncSession, user_id: str, post_id: str, response_result: str):
    try:
        log_entry = PopLog(
            user_id=user_id,
            time=datetime.now(),
            post_id=post_id,
            type="修改",
            response_result=response_result,
            result="獲取修改鏈接成功" if "好的" in response_result else "獲取修改鏈接失敗"
        )
        db.add(log_entry)
        await db.commit()
        logger.info(f"數據插入成功: user_id={user_id}, post_id={post_id}")
    except Exception as e:
        await db.rollback()
        logger.error(f"數據庫操作失敗: {str(e)}")
        raise HTTPException(status_code=500, detail="數據庫操作失敗")

router = APIRouter()

@router.post("/submit-url")
async def submit_url(request: URLRequest, db: AsyncSession = Depends(get_db)):
    try:
        # 解析URL參數
        parsed_url = urlparse(request.url)
        query_params = parse_qs(parsed_url.query)
        
        params_value = query_params.get('params', [None])[0]
        decoded_json = json.loads(params_value)
        
        # 驗證物件編號格式
        match = re.match(r'^[rR](\d+)$', request.post_id)
        if match:
            post_id_number = match.group(1)
            new_url = f"https://user.591.com.tw/post/two/rent/{post_id_number}"
            response_result = "非出售物件，僅提供物件管理鏈接"
            
            # 獲取用戶ID並記錄日誌
            c_cf_dialogueDesc = decoded_json.get('c_cf_dialogueDesc', '')
            params_dict = parse_qs(c_cf_dialogueDesc)
            user_id = params_dict.get('user_id', ['unknown'])[0]
            
            await insert_to_database(db, user_id, request.post_id, response_result)
            
            return {
                "ret_code": 0,
                "ret_message": f"<p>您可點擊鏈接進入物件資料修改頁面：</p><p><a href=\"{new_url}\" target=\"_blank\">{new_url}</a></p>"
            }
            
        # 調用591 API
        url = "https://api.591.com.tw/api/sobot/wareEdit/getUrl"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type": "application/json"
        }
        
        c_cf_dialogueDesc_value = "{\"c_cf_dialogueDesc\":\"" + decoded_json.get('c_cf_dialogueDesc', '') + "\"}"
        payload = {
            "post_id": request.post_id,
            "params": c_cf_dialogueDesc_value
        }
        
        response = requests.post(url, json=payload, headers=headers)
        response_data = response.json()
        response_result = response_data.get('errorMsg', 'Unknown error')
        
        # 獲取用戶ID並記錄日誌
        params = json.loads(c_cf_dialogueDesc_value)
        c_cf_dialogueDesc = unquote(params['c_cf_dialogueDesc'])
        params_dict = parse_qs(c_cf_dialogueDesc)
        user_id = params_dict.get('user_id', ['unknown'])[0]
        
        await insert_to_database(db, user_id, request.post_id, response_result)
        
        return {"ret_code": 0, "ret_message": response_result}
        
    except Exception as e:
        logger.error(f"處理請求失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))