import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '../stores/user'

// 擴展 Window 接口以包含自定義屬性
declare global {
  interface Window {
    messageListenerAdded?: boolean
  }
}

const routes: Array<RouteRecordRaw> = [
  {
    path: '/index',
    name: '首頁',
    component: () => import('../views/index.vue')
  },
  {
    path: '/CancelPost',
    name: '取消租售物件',
    component: () => import('../views/CancelPost.vue')
  },
  {
    path: '/CancelExtend',
    name: '取消加值服務',
    component: () => import('../views/CancelExtend.vue')
  },
  {
    path: '/SaleCancelPackage',
    name: '取消出售套餐',
    component: () => import('../views/SaleCancelPackage.vue')
  },
  {
    path: '/RentCancelPackage',
    name: '取消出租套餐',
    component: () => import('../views/RentCancelPackage.vue')
  },
  {
    path: '/ModifyPost',
    name: '修改物件',
    component: () => import('../views/ModifyPost.vue')
  },
  {
    path: '/DelayPackage',
    name: '套餐延期',
    component: () => import('../views/DelayPackage.vue')
  },
  {
    path: '/Get711PayNumber',
    name: '7-11儲值',
    component: () => import('../views/Get711PayNumber.vue')
  },
  {
    path: '/DeletePriceFluctuations',
    name: '清空價格波動',
    component: () => import('../views/DeletePriceFluctuations.vue')
  },
  {
    path: '/',
    redirect: '/index'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫：仅在同一会话内保持参数
router.beforeEach((to, _from, next) => {
  const userStore = useUserStore()

  document.title = to.meta.title as string || '591自助服務系統'

  // 如果 store 中没有数据，才从 URL 中获取
  if (!userStore.originalData) {
    // 存储完整的 search 字符串
    userStore.setOriginalData(window.location.search)
  }

  if (to.meta.requiresAuth && !userStore.originalData) {
    alert('請從591首頁進入頁面')
    next('/index')
    return
  }
  next()
})

export default router
